myCount=3
myH1={"1_23434":"4785885","1_336577":"4786"}
myH2={"2_23434":"4785885","2_336577":"4786"}
myH3={"3_23434":"4785885","3_336577":"4786","3_56636577":"4786"}
# my={"1_23434":"4785885","1_336577":"4786","2_23434":"4785885","2_336577":"4786","3_23434":"4785885","3_336577":"4786","3_56636577":"4786"}
my123=[myH1,myH2,myH3]
myResult={}

count=0
while True:

    for my_123 in my123:
        if count<len(my123[count]):
            temp=0
            for key in my_123.keys():
                if temp==count:
                    myResult[key]=my_123.get(key)
                    break
                temp+=1
    count+=1
    if count>=myCount:
        break
def myTasks(myTask):
    myResult={}
    count = 0
    while True:

        for task in myTask:
            if count < len(myTask[count]):
                temp = 0
                for key in task.keys():
                    if temp == count:
                        myResult[key] = task.get(key)
                        break
                    temp += 1
        count += 1
        if count >= myCount:
            break
    return myResult
print(myResult)


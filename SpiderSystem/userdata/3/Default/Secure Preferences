{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "manifest_permissions": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13359191290498532", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "在这里，您可以找到适用于 Chromium 的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 网上应用店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "never_activated_since_loaded": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\chrome\\74.0.3729.108\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "kmendfapggjehodndflmmgagdbamhnfd": {"active_permissions": {"api": ["cryptotokenPrivate", "externally_connectable.all_urls", "hid", "tabs", "u2fDevices", "usb", {"usbDevices": [{"interfaceClass": -1, "interfaceId": -1, "productId": 529, "vendorId": 4176}]}, "webConnectable"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13359191290500631", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["util.js", "b64.js", "cbor.js", "sha256.js", "timer.js", "countdown.js", "countdowntimer.js", "devicestatuscodes.js", "approvedorigins.js", "errorcodes.js", "webrequest.js", "messagetypes.js", "factoryregistry.js", "closeable.js", "requesthelper.js", "asn1.js", "enroller.js", "requestqueue.js", "signer.js", "origincheck.js", "textfetcher.js", "appid.js", "watchdog.js", "logging.js", "webrequestsender.js", "window-timer.js", "cryptotokenorigincheck.js", "cryptotokenapprovedorigins.js", "gnubbydevice.js", "hidgnubbydevice.js", "usbgnubbydevice.js", "gnubbies.js", "gnubby.js", "gnubby-u2f.js", "gnubbyfactory.js", "singlesigner.js", "multiplesigner.js", "generichelper.js", "inherits.js", "individualattest.js", "devicefactoryregistry.js", "usbhelper.js", "usbenrollhandler.js", "usbsignhandler.js", "usbgnubbyfactory.js", "googlecorpindividualattest.js", "cryptotokenbackground.js"]}, "description": "CryptoToken Component Extension", "externally_connectable": {"accepts_tls_channel_id": true, "ids": ["fjajfjhkeibgmiggdfehjplbhmfkialk"], "matches": ["https://*/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB", "manifest_version": 2, "name": "CryptoTokenExtension", "permissions": ["hid", "u2fDevices", "usb", "cryptotokenPrivate", "externally_connectable.all_urls", "tabs", "https://*/*", "http://*/*", {"usbDevices": [{"productId": 529, "vendorId": 4176}]}], "version": "0.9.74"}, "never_activated_since_loaded": true, "path": "C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\chrome\\74.0.3729.108\\resources\\cryptotoken", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mfehgcgbbipciphmccgaenjidiccnmng": {"active_permissions": {"api": ["cloudPrintPrivate"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13359191290496797", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://www.google.com/cloudprint"}, "urls": ["https://www.google.com/cloudprint/enable_chrome_connector"]}, "description": "Cloud Print", "display_in_launcher": false, "icons": {}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB", "name": "Cloud Print", "permissions": ["cloudPrintPrivate"], "version": "0.1"}, "never_activated_since_loaded": true, "path": "C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\chrome\\74.0.3729.108\\resources\\cloud_print", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "resourcesPrivate"], "explicit_host": ["chrome://resources/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13359191290501969", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "contentSettings", "metricsPrivate", "resourcesPrivate", {"fileSystem": ["write"]}], "version": "1"}, "never_activated_since_loaded": true, "path": "C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\chrome\\74.0.3729.108\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"active_permissions": {"api": ["desktopCapture", "processes", "webConnectable", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13359191290499725", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.google.com/*", "*://localhost/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["desktopCapture", "enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate"], "version": "1.3.13"}, "never_activated_since_loaded": true, "path": "C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\chrome\\74.0.3729.108\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "3B1524D43BFA21B99F43B1FA00F8C7879A4363FBB40B60E2543CCA66550B994D"}, "default_search_provider_data": {"template_url_data": "BF4EE18B461DB8A3C722050E6897CD25B1036736EB0598095254FFB22083E800"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "9022FA07BD884A9E8D4D3DD8317EE617CD5BE7868D7BEF851FE9ED3F1CA3CB33", "kmendfapggjehodndflmmgagdbamhnfd": "D5EB027D5AF62B4FD98E89DBD85711CCA47BF2EA80A0D54D8B9907D2BE034682", "mfehgcgbbipciphmccgaenjidiccnmng": "A02B675121B46B636D5D440A2186D5D82BD8EB941D0FB4E4912BD29C06CAB592", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "00E4F5005BA0A2462142DD8B557BC53034D231E8DBBBE1CA90EBB3E8AA8E4609", "nkeimhogjdpnpccoofpliimaahmaaome": "EF42EEAF625D1FB7CA4257A2D3518B69464C8E17E06205302979A10B5C986EBE"}}, "google": {"services": {"account_id": "1626D922A6A6F9C94F2C48CFB188E0396C8142629A85A9834F55E54D50AEAC81", "last_account_id": "ED82269A0E33D1B3BBB39835EAFF240179A2FE4EDE4691245C676FCA23181961", "last_username": "42AA6824D70557FAA57D1AF0C08C3B38AD49E98202A545DE6780F5BCF56BD254", "username": "ADD6E392258DB8883B4C4AE6613F86A7115E13F4129AB8B100F468057926D76F"}}, "homepage": "70DC9AFD566F1364AB5C21472DFE9F1DC29D55F5B930014F16E9BF187EBC06F2", "homepage_is_newtabpage": "0FB5A1B45BE6BEAAEADA079C3CC8F065B68055FAAE0B3DA64EB6B2B56ECD7B45", "media": {"storage_id_salt": "D582A7C566DBA2433557FC1F0FA49A79770E559D7E8720C59DCD2C68EC39A68A"}, "pinned_tabs": "4F8C16ABAFF79E87CE70C2559C08CA73C22368DC543C79613A7F2478992C4E0E", "prefs": {"preference_reset_time": "2EC91EF1E1EA4C32B4F562D9DFEB808B31924078A59D27E5812B52F617E5E1F3"}, "safebrowsing": {"incidents_sent": "6808F0F44791CEC75E825415745BB284C8E59F8188E3EC57B808AEB0C94D59E5"}, "search_provider_overrides": "C457D6B437D1811CFA07F5716AE9B712F23DF1B5AB57E655BDE1F695E78EB6ED", "session": {"restore_on_startup": "5204547B98B46454F0232B7D4DC3032ECDC246D629A8DA9C6BF5F6C746490FC6", "startup_urls": "FCB4F4553078818342C5EDA2DD33517CD5D8423DED054BC39C3013211C6C136A"}, "settings_reset_prompt": {"last_triggered_for_default_search": "70FCF0CFA2B0472C2AC57BC367EF40A6951E7FE9106AFDD9955B207E42D138B7", "last_triggered_for_homepage": "289C9AD911209F22D1E20F446E17EF5BDDE29F56C4AA0B5FE273D0A92B62EFD1", "last_triggered_for_startup_urls": "E9EAE77FA6B13BA82F7C3D0703A530BB4A1A95768076BED8F6F6DF436494A091", "prompt_wave": "5AE8CF1BFE27763B6D53A2F377663B430C273BE264D972154EEBD1FAB2DBE7E6"}, "software_reporter": {"prompt_seed": "6499015653E379E51503AE842D1B295C0ED14E67DE63F1122960945DCC06ACA0", "prompt_version": "4661072ACBDA0F2B938CDC6AA54B6F9C7E181D86E5EABBB27C086984D398A072", "reporting": "BF2D65A2B8713130088E05B4809684259592227AE9BD352408E070DDCE2F3880"}}, "super_mac": "1574D2D88F4ED76899A19B9EBEDB66A65CCEC62734B6EB18D1046FE9CA2FDA5F"}}
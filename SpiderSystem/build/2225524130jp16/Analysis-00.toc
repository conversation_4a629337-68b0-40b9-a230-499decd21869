(['C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\2225524130jp16.py'],
 ['C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem'],
 [],
 ['C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\numpy\\_pyinstaller',
  'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 [],
 [],
 '3.8.10 (tags/v3.8.10:3d8993a, May  3 2021, 11:48:03) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('2225524130jp16',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\2225524130jp16.py',
   'PYSOURCE')],
 [('_pyi_rth_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python38\\lib\\zipimport.py', 'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Program Files\\Python38\\lib\\contextlib.py', 'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python38\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python38\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python38\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python38\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python38\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'C:\\Program Files\\Python38\\lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python38\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python38\\lib\\bz2.py', 'PYMODULE'),
  ('struct', 'C:\\Program Files\\Python38\\lib\\struct.py', 'PYMODULE'),
  ('email', 'C:\\Program Files\\Python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python38\\lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python38\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python38\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python38\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python38\\lib\\selectors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python38\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python38\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python38\\lib\\getopt.py', 'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python38\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python38\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python38\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python38\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python38\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python38\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python38\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python38\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python38\\lib\\opcode.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python38\\lib\\pathlib.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python38\\lib\\typing.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python38\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python38\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('stringprep', 'C:\\Program Files\\Python38\\lib\\stringprep.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python38\\lib\\bisect.py', 'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python38\\lib\\hmac.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python38\\lib\\ipaddress.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python38\\lib\\hashlib.py', 'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python38\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Program Files\\Python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python38\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python38\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python38\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python38\\lib\\tempfile.py', 'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python38\\lib\\ssl.py', 'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python38\\lib\\queue.py', 'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('__future__', 'C:\\Program Files\\Python38\\lib\\__future__.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('cgitb', 'C:\\Program Files\\Python38\\lib\\cgitb.py', 'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python38\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Program Files\\Python38\\lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python38\\lib\\glob.py', 'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'C:\\Program Files\\Python38\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python38\\lib\\tty.py', 'PYMODULE'),
  ('subprocess', 'C:\\Program Files\\Python38\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python38\\lib\\signal.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python38\\lib\\sysconfig.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python38\\lib\\platform.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python38\\lib\\random.py', 'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python38\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python38\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python38\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python38\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python38\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python38\\lib\\gzip.py', 'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python38\\lib\\base64.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python38\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Program Files\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Program Files\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('PyQt5.Qt5', '-', 'PYMODULE')],
 [('PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtMultimedia\\declarative_multimedia.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtMultimedia\\declarative_multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\qtquickscene2dplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\qtquickscene2dplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\qtquickscene3dplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\qtquickscene3dplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'BINARY'),
  ('python38.dll', 'C:\\Program Files\\Python38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\ssleay32.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\ssleay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libeay32.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libeay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('_lzma.pyd', 'C:\\Program Files\\Python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python38\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python38\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\Qt.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt.pyd',
   'EXTENSION'),
  ('PyQt5\\QtXmlPatterns.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtXmlPatterns.pyd',
   'EXTENSION'),
  ('PyQt5\\QtXml.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtXml.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWinExtras.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtWinExtras.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebSockets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtWebSockets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebChannel.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtWebChannel.pyd',
   'EXTENSION'),
  ('PyQt5\\QtTextToSpeech.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtTextToSpeech.pyd',
   'EXTENSION'),
  ('PyQt5\\QtTest.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtTest.pyd',
   'EXTENSION'),
  ('PyQt5\\QtSvg.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtSvg.pyd',
   'EXTENSION'),
  ('PyQt5\\QtSql.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtSql.pyd',
   'EXTENSION'),
  ('PyQt5\\QtSerialPort.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtSerialPort.pyd',
   'EXTENSION'),
  ('PyQt5\\QtSensors.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtSensors.pyd',
   'EXTENSION'),
  ('PyQt5\\QtRemoteObjects.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtRemoteObjects.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuickWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtQuickWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuick3D.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtQuick3D.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuick.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtQuick.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQml.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtQml.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPositioning.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtPositioning.pyd',
   'EXTENSION'),
  ('PyQt5\\QtOpenGL.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtOpenGL.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNfc.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtNfc.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt5\\QtMultimediaWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtMultimediaWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtMultimedia.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtMultimedia.pyd',
   'EXTENSION'),
  ('PyQt5\\QtLocation.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtLocation.pyd',
   'EXTENSION'),
  ('PyQt5\\QtHelp.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtHelp.pyd',
   'EXTENSION'),
  ('PyQt5\\QtDesigner.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtDesigner.pyd',
   'EXTENSION'),
  ('PyQt5\\QtDBus.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtDBus.pyd',
   'EXTENSION'),
  ('PyQt5\\QtBluetooth.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtBluetooth.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('libffi-7.dll', 'C:\\Program Files\\Python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python38\\python3.dll', 'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Program Files\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python38\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-1.8\\jre\\bin\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-1.8\\jre\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\build\\2225524130jp16\\base_library.zip',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\cryptography-42.0.5.dist-info\\METADATA',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtxmlpatterns_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtxmlpatterns_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebsockets_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebsockets_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtserialport_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtserialport_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene2D\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtMultimedia\\Video.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtMultimedia\\Video.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtMultimedia\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtMultimedia\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Scene3D\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtMultimedia\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtMultimedia\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtconnectivity_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtconnectivity_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SpiderSystem\\venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA')])

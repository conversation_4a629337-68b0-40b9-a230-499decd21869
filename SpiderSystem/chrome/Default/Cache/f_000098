if(typeof lwx == 'undefined'){
    window.onload=function(){
        var ajaxurl =  'https://yisuapi.yisu.com/index.php/Speed/uc/wx/?callback=?';
        var query = new Object();
        var urll = location.href.split('#')[0];
        var shareimg = 'https://cache.yisu.com/www/images/yisulogo-300-300.png';
        query.urll = encodeURIComponent($.trim(urll));
        query.post_type = "json";
        $.ajax({
            url: ajaxurl,
            data:query,
            type: "GET",
            dataType: "jsonp",
            success: function(ress){
                wx.config({
                    debug: false,
                    appId: ress.appid,
                    timestamp: ress.timestamp,
                    nonceStr: ress.nonceStr,
                    signature: ress.signature,
                    jsApiList: ['onMenuShareTimeline','onMenuShareAppMessage']
                });
                wx.ready(function(){
                    wx.checkJsApi({
                        jsApiList: ['onMenuShareTimeline','onMenuShareAppMessage'],
                        success: function(res) {
                        }
                    });
                    wx.onMenuShareTimeline({
                        title: '<?php echo $this->title; ?>', // 分享标题
                        link: urll, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: shareimg, // 分享图标
                        success: function () {
                            // 用户确认分享后执行的回调函数
                        },
                        cancel: function () {
                            // 用户取消分享后执行的回调函数
                        }
                    });
                    wx.onMenuShareAppMessage({
                        title: '<?php echo $this->title; ?>', // 分享标题
                        desc: '<?php echo $this->description; ?>', // 分享描述
                        link: urll, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: shareimg, // 分享图标
                        type: '', // 分享类型,music、video或link，不填默认为link
                        dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
                        success: function () {
                            // 用户确认分享后执行的回调函数
                        },
                        cancel: function () {
                            // 用户取消分享后执行的回调函数
                        }
                    });
                });
            }
            ,error:function(){
                console.log("通信失败");
            }
        });
    }
}
var default_value=0;
$(function(){
    $('.service_list .row').hover(function () {
        $(this).find('.service_wechat').show();
    },function () {
        $('.service_list .row').find('.service_wechat').hide();
    });

	$(".cms-hdm-tits a").mouseenter(function(){
		$(this).siblings().removeClass("on");
		$(this).addClass("on");
		$(this).parents(".alls").find(".cms-hdm-conts:eq("+$(this).index()+")").css("display","block");
		$(this).parents(".alls").find(".cms-hdm-conts:eq("+$(this).index()+")").siblings(".cms-hdm-conts").css("display","none");
		});
		
	$(".cms-hdm-tit a").click(function(){
		$(this).siblings().removeClass("on");
		$(this).addClass("on");
		$(this).parents(".alls").find(".cms-hdm-conts:eq("+$(this).index()+")").css("display","block");
		$(this).parents(".alls").find(".cms-hdm-conts:eq("+$(this).index()+")").siblings(".cms-hdm-conts").css("display","none");
		});
	
	$("body").on('click', '.singleTab a:not(".dis")', function(){
		$(this).siblings().removeClass("selected");
		$(this).addClass("selected");
		});
	$("body").on('click', '.multTab a:not(".dis")', function(){
		$(this).toggleClass("selected");
		});

	var href = window.location.href;
	if( window.navigator.cookieEnabled ){
        var ms = Date.parse(new Date()) + 1000 * 60 * 60 * 24 * 1,
            expires = new Date(ms);
		document.cookie	= 'previousUrl= '+encodeURI(href)+'; expires= '+expires+'; path=/;';
	}

	
	
	//有提示
	$("body").on('click', '.havaTips', function(){
		var self = $(this),
			posX = 0,
			posY = 0,
			tps2W = 0,
			tps = '',
			tpsCls = '';
			tps = self.attr('dataTips');
			tpsCls = self.parent().attr('dataCls');
			$('.' + tpsCls + ' .ccs').html(tps);
			tps2W = $('.' + tpsCls).width() / 2 - 54;
			posX = self.offset().left - tps2W;
			posY = self.offset().top - 40;
			$('.' + tpsCls).css({left: posX + 'px', top: posY + 'px', display: 'block'});
		});

	//顶部导航
	/*$(".nav li").hover(function(){
		var lft = ($(this).outerWidth() - 202) / 2 + 38;
		$(this).find('.chi').css('left', lft + 'px');
		if(!$(this).find(".chi").is(":animated")){
			  $(this).find(".chi").slideDown();
			}
	  },function(){
	    $(this).find(".chi").slideUp();
	  });
	  
	//左侧导航
	$(".navs li .fir").click(function(){
		$(this).parent().siblings().removeClass("curs");
		$(this).parent().toggleClass('curs');
	});*/
	
	//返回顶部
	$(".rightBar .last").click(function(){
		$('html,body').stop().animate({scrollTop:0},{
			  duration:800,
			  easing:'easeInOutExpo'
			});
	});

	$('.rightBar .csIn .flt_l .wx').hover(function(){
		  var posT = $(this).offset().top - $(window).scrollTop(),
		  	  posL = $(this).offset().left,
			  src = $(this).attr('ewmUrl');
			  ele = $('.kfEwmR');
			  ele.find('img').attr('src', src);
			  ele.css({left : (posL + 25) + 'px', top : (posT - 20) + 'px', display : 'block'});
		},function(){
			  $('.kfEwmR').css({display : 'none'});
		});
	$('.rightBar .csIn .flt_r .wx').hover(function(){
		  var posT = $(this).offset().top - $(window).scrollTop(),
		  	  posL = $(this).offset().left,
			  src = $(this).attr('ewmUrl');
			  ele = $('.kfEwmL');
			  ele.find('img').attr('src', src);
			  ele.css({left : (posL - 168) + 'px', top : (posT - 20) + 'px', display : 'block'});
		},function(){
			  $('.kfEwmL').css({display : 'none'});
		});
	
	setVisitRecord();

});

function initTps(cls){
	var self = $('.' + cls),
		posX = 0,
		posY = 0,
		tps2W = 0,
		tps = '',
		tpsCls = '';
		tps = self.attr('dataTips');
		tpsCls = self.parent().attr('dataCls');
		$('.' + tpsCls + ' .ccs').html(tps);
		tps2W = $('.' + tpsCls).width() / 2 - 54;
		posX = self.offset().left - tps2W;
		posY = self.offset().top - 40;
		$('.' + tpsCls).css({left: posX + 'px', top: posY + 'px', display: 'block'});
	}


function getqueryStr(name) {
    var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if(r!=null) return unescape(r[2]);
    return null;
}

/*ie兼容*/
function myBrowser(){
    var userAgent = navigator.userAgent;
    var isOpera = userAgent.indexOf("Opera") > -1;
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
    if (isIE) {
        var IE5 = IE55 = IE6 = IE7 = IE8 = IE9 = IE10 = IE11=false;
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        IE55 = fIEVersion == 5.5;
        IE6 = fIEVersion == 6.0;
        IE7 = fIEVersion == 7.0;
        IE8 = fIEVersion == 8.0;
        IE9 = fIEVersion == 9.0;
        IE10 = fIEVersion == 10.0;
        IE11 = fIEVersion == 11.0;

        if (IE55) {
            return "IE55";
        }
        if (IE6) {
            return "IE6";
        }
        if (IE7) {
            return "IE7";
        }
        if (IE8) {
            return "IE8";
        }
        if (IE9) {
            return "IE9";
        }
    }
}
function fix_ie_placeholder(){
    $("[placeholder]").each(function(){
        var el = $(this);
        var placeholder = el.attr("placeholder");
        if(el.prop("type")=="password") {
            el.focus(function ()
            {
                $(this).prop("type","password");
                if($(this).val() == $(this).attr("placeholder"))
                {
                    $(this).val('').removeClass("placeholderColor");
                }
            }).blur(function ()
            {
                if($(this).val()=='')
                {
                    $(this).prop("type","text");
                    $(this).val($(this).attr("placeholder")).addClass("placeholderColor");
                }
            }).blur();

            if(el.val()==''||el.val()==el.attr("placeholder"))
            {
                el.prop("type","text");
                el.val($(this).attr("placeholder")).addClass("placeholderColor");
            }
        } else {
            el.focus(function ()
            {
                if($(this).val() == $(this).attr("placeholder"))
                {
                    $(this).val('').removeClass("placeholderColor");
                }
            }).blur(function ()
            {
                if($(this).val()=='')
                {
                    $(this).val($(this).attr("placeholder")).addClass("placeholderColor");
                }
            });

            if(el.val()==''||el.val()==el.attr("placeholder"))
            {
                el.val($(this).attr("placeholder")).addClass("placeholderColor");
            }
        }
    });
};
if(myBrowser() == "IE9"){
    fix_ie_placeholder();
}
/*ie兼容*/

// 警告提示弹窗（需用户确认）
$(function(){
    (function ($) {
        $.yisu_alert_warn = function(options) {
            var dft = {
                cont:"ys_msg"
            };
            if($('.yisu_pc_tips').length != 0){
                return;
            }else{
                var ops = $.extend(dft,options||{});
                var box = $("<div></div>").addClass("yisu_pc_tips").appendTo($("body"));
                box.html("<div class='content'>"+
                    "<div class='head'>温馨提示<span>×</span></div>" +
                    "<div class='body'><div class='tips_body'><span></span><p>"+ ops.cont +"</p></div></div>" +
                    "<div class='bottom'><button type='button'>确 认</button></div></div>"
                )
                box.children('.content').show().animate( { opacity : 1, top : 200 + 'px' },300 )
                $('.yisu_pc_tips .head span,.yisu_pc_tips .bottom button').on('click',function(){
                    box.remove()
                })
            }
        };
    })(jQuery);
})

// 警告提示弹窗（自动关闭）
$(function(){
    (function ($) {
        $.yisu_autoclose_warn = function(options) {
            var dft = {
                cont:"ys_msg"
            };
            if($('.yisu_autoclose_tips').length != 0){
                return;
            }else{
                var ops = $.extend(dft,options||{});
                var box = $("<div></div>").addClass("yisu_autoclose_tips").appendTo($("body"));
                box.html("<div class='head'>温馨提示</div>" +
                    "<div class='body'><div class='tips_body'><span></span><p>"+ ops.cont +"</p></div>"
                )
                box.show().animate( { opacity : 1, top : 200 + 'px' },300 )
                setTimeout(function(){
                    box.animate({opacity:0},1000,function(){
                        box.remove();
                    });
                },750);
            };
        };
    })(jQuery);
})
// vip提示弹窗
$(function(){
    (function ($) {
        $.yisu_vip_tips = function(options) {
            var dft = {
                cont:"ys_msg"
            };
            if($('.vip_tips').length != 0){
                return;
            }else{
                var ops = $.extend(dft,options||{});
                var box = $("<div></div>").addClass("vip_tips").appendTo($("body"));
                box.html("<div class='content'>"+
                    "<div class='head'>温馨提示<span>×</span></div>" +
                    "<div class='body'>"+
                    "<div class='tips_body'>"+
                    "<div>本区只对VIP用户开放，认证成为亿速云VIP用户，需要提供如下信息：</div>"+
                    "<p>1. 目前只对企业、事业单位开放认证；</p>"+
                    "<p>2. 提供企业营业执照盖章电子版；</p>"+
                    "<p>3. 企业与亿速云签定服务协议并信息安全承诺书；</p>"+
                    "<p>4. 提交本企事业单位办公场所的实拍照片</p>"+
                    "<span class='service'>详细可以与售前客服联系！她们会为您解答及协助您认证。</span>"+
                    "</div>"+
                    "</div>" +
                    "<div class='bottom'><button type='button'>立即联系客服</button></div>"+
                    "</div>"
                )
                box.children('.content').show().animate( { opacity : 1, top : 200 + 'px' },300 )
                $('.vip_tips .head span').on('click',function(){
                    box.remove()
                    $('.customer_service').hide();
                })
                $('.vip_tips .bottom button').on('click',function () {
                    $('.customer_service').show();
                });
            }
        };
    })(jQuery);
})

//去左空格;
function ltrim(s){
    return s.replace(/(^\s*)/g, "");
}

//去右空格;
function rtrim(s){
    return s.replace(/(\s*$)/g, "");
}

//去左右空格;
function trim(s){
    return s.replace(/(^\s*)|(\s*$)/g, "");
}

//判断是否为空
function isEmpty(str){
    //过滤空字符串
    str = str.replace(/(^\s*)|(\s*$)/g, "");
    if(str == null || str == undefined || str == ''){
        return true;
    }
    return false;
}

var cache_host = "https://cache.yisu.com/";
//资讯、问答产品导航-所有产品
function parseInfoMenuProducts() {
    $.getJSON(cache_host + "www/all_products.json", {}, function parseResult(result){
        var all_products = result;
        var menu_html = '<div class="menu-item-box clearfix">';
        for (var i = 0, len = all_products.length; i < len; i++) {
            menu_html += '<div class="menu-item ' + (i+1 == len ? 'last' : '') + '">' +
                '             <dl>' +
                '                 <dt>' + all_products[i]['className'] + '</dt>';
            var products = all_products[i]['products'];

            for (var j = 0, plen = products.length; j < plen; j++) {
                var tips = '';
                if (products[j]['tip_type'] == 1) {
                    tips = 'HOT';
                } else if (products[j]['tip_type'] == 2) {
                    tips = 'NEW';
                }
                menu_html += '<dd>' +
                    '             <a href="' + products[j]['link'] + '">' +
                    '                 <span>' + products[j]['productName'] + '</span>' +
                    '                 <i>' + tips + '</i>' +
                    '             </a>';
                if (products[j]['childs'].length > 0) {
                    var childs = products[j]['childs'];
                    menu_html += '<div class="second-level-link">';
                    for (var k = 0, clen = childs.length; k < clen; k++) {
                        menu_html += '<a href="' + childs[k]['link'] + '"><span>' + childs[k]['childName']+ '</span><i></i></a>';
                    }
                    menu_html += '</div>';
                }
                menu_html += '</dd>';
            }

            menu_html += '</dl></div>';

        }
        menu_html += '</div>';

        $(".pulldown-menu").html(menu_html);
    });
}

//资讯、问答产品导航-热门产品
function parseInfoMenuHot() {
    $.getJSON(cache_host + "www/hot_products.json", {}, function parseResult(result){
        var hot_products = result;
        var hot_html = '<div class="hotlink">' +
            '    <div class="hotlink-box">' +
            '        <span class="hot">热门</span>'+
            '        <ul class="clearfix">';
        for (var i = 0, len = hot_products.length; i < len; i++) {
            hot_html += '<li>' +
                '<a class="' + (hot_products[i]['is_select'] ? 'selected' : '') + '" href="' + hot_products[i]['link'] + '"><span>' + hot_products[i]['productName'] + '</span></a>' +
                '</li>';
        }
        hot_html += '</ul></div></div>';
        $(".pulldown-menu").append(hot_html)
    });
}
//检测搜索引擎
function check_referer() {
    var referer_link = document.referrer;
    if (referer_link == undefined || referer_link == '') {
        //检查是否指定页面
        var pattern1 = /yisu.com\/(zixun|ask)\/tags\/(\d)+/;
        var pattern2 = /yisu.com\/(zixun|ask)\/(\d+).html/;
        var pattern3 = /yisu.com\/(zixun|ask)\/user\/(\d+)/;
        var current_page = window.location.href;
        if (pattern1.test(current_page) || pattern2.test(current_page) || pattern3.test(current_page)) {
            return '';
        }
        return false;
    }

    var refer_host = '';
    var urlReg=/http(s)?:\/\/([^\/]+)/i;
    refer_host = referer_link.match(urlReg)[2];

    var search_engines = [
        'baidu.com',    //百度
        'google.com',   //谷歌
        'bing.com',     //必应
        'so.com',        //360
        'sogou.com',    //搜狗
        'sm.cn',        //神马
        'toutiao.com'   //头条
    ];

    var flag = false;
    for (var i = 0, len = search_engines.length; i < len; i++) {
        if (refer_host.indexOf(search_engines[i]) > -1) {
            flag = true;
            break ;
        }
    }
    console.log(flag);
    if (!flag) {
        return false;
    }
    return refer_host;
}
//记录访问记录
function setVisitRecord()
{
    var flag = check_referer();
    var visit_token = sessionStorage.getItem('visitToken');
    if (flag === false && visit_token != undefined && visit_token != '' && visit_token != null) {
        $.ajax({
            type: "GET",
            url: "https://yisuapi.yisu.com/speed/information/setVisitRecord/?callback=?",
            dataType: "jsonp",
            data: {visit_token : visit_token, current_page : encodeURIComponent(window.location.href)},
            timeout: 30000,
            success: function (res) {

            }
        });
    }
}
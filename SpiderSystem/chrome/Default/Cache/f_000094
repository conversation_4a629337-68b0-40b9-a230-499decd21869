@charset "utf-8";
/*
    created 2019-11-12
    database page
*/
/*======= common nav start =======*/
.yisulayer {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  z-index: 1000;
  transition: all 0.4s;
}
.yisulayer:hover {
  background-color: #323A45;
}
.yisu-mainnav {
  height: 80px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.yisu-nav {
  width: 1200px;
  height: 79px;
  margin: 0 auto;
}
.yisu-nav > div {
  float: left;
}
.yisu-logo a {
  display: block;
  width: 132px;
  height: 79px;
}
.yisu-logo a img {
  margin-top: 21px;
}
.yisu-link {
  margin-left: 60px;
}
.yisu-link > ul > li {
  float: left;
  height: 100%;
  margin-right: 22px;
}
.yisu-link > ul > li.last-nav-link {
  margin-right: 0px;
}
.yisu-link > ul > li > a {
  display: block;
  height: 79px;
  line-height: 79px;
  font-size: 18px;
  color: #ffffff;
  padding: 0px 9px;
  border-bottom: 2px solid transparent;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.yisu-link > ul > li:hover > a {
  color: #FF6666;
  border-bottom-color: #FF6666;
}
.yisu-link > ul > li.currentpage > a {
  color: #FF6666;
}
.yisu-link > ul > li > a.moreserver > span {
  display: inline-block;
  width: 11px;
  height: 6px;
  background: url(../images/index_sprite_img.png) no-repeat -364px -177px;
  margin-left: 6px;
  vertical-align: middle;
}
.yisu-link > ul > li:hover a.moreserver > span,
.yisu-link > ul > li.currentpage > a > span {
  background: url(../images/index_sprite_img.png) no-repeat -385px -177px;
}
.yisu-nav .yisu-login {
  float: right;
  padding-top: 22px;
}
.yisu-login a {
  display: inline-block;
  width: 98px;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  text-align: center;
  color: #ffffff;
  border: 1px solid #fff;
  border: 1px solid rgba(255, 255, 255, 0.5);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.yisu-login a.register {
  background-color: #FF6666;
  border-color: #FF6666;
  margin-right: 10px;
}
.yisu-login a.register:hover {
  color: #FFF;
  background-color: #FF4444;
  border-color: #FF4444;
}
.yisu-login a.loginbtn:hover {
  color: #FF6666;
  background-color: transparent;
  border-color: #FF6666;
  border-color: rgba(255, 102, 102, 0.5);
}
.yisuserver-nav {
  width: 100%;
}
.yisuserver-box {
  position: relative;
  width: 1200px;
  padding-left: 364px;
  margin: 0 auto;
}
.yisuserver-box > span.hot {
  position: absolute;
  left: 310px;
  top: 10px;
}
.yisuserver-box ul li {
  float: left;
  margin-right: 30px;
}
.yisuserver-box ul li.last {
  margin-right: 0px;
}
.yisuserver-box ul li a {
  display: block;
  height: 39px;
  line-height: 39px;
  font-size: 15px;
  color: #ffffff;
  padding: 0px 5px;
  border-bottom: 2px solid transparent;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.yisuserver-box ul li a:hover {
  color: #FF6666;
  border-color: #FF6666;
}
.pulldown-menu {
  position: absolute;
  left: 0px;
  right: 0px;
  top: 80px;
  min-height: 570px;
  min-width: 1200px;
  max-width: 100%;
  background-color: #323A45;
  padding-top: 25px;
  padding-bottom: 110px;
  z-index: 999;
  display: none;
  opacity: 0;
}
.pulldown-menu .menu-item-box {
  width: 1200px;
  margin: 0 auto;
}
.pulldown-menu .menu-item-box .menu-item {
  float: left;
  width: 200px;
  margin-right: 50px;
}
.pulldown-menu .menu-item-box .menu-item.last {
  margin-right: 0px;
}
.pulldown-menu .menu-item-box .menu-item dl dt {
  position: relative;
  line-height: 64px;
  font-size: 16px;
  color: #FFFEFE;
  border-bottom: 1px solid #515761;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 20px;
}
.pulldown-menu .menu-item-box .menu-item dl dt > a {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  color: #FFFEFE;
}
.pulldown-menu .menu-item-box .menu-item dl dt > a .arrow {
  position: absolute;
  right: 0px;
  top: 28px;
  width: 11px;
  height: 11px;
  background: url(../images/right-arrow-white.png) no-repeat center;
  background-size: 11px 11px;
}
.pulldown-menu .menu-item-box .menu-item dl dt > a:hover {
  color: #FF6666;
}
.pulldown-menu .menu-item-box .menu-item dl dt > a:hover .arrow {
  background: url(../images/right-arrow-red.png) no-repeat center;
  background-size: 11px 11px;
}
.pulldown-menu .menu-item-box .menu-item dl dd {
  margin-bottom: 12px;
}
.pulldown-menu .menu-item-box .menu-item dl dd a {
  display: block;
  width: 210px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #FFFEFE;
  padding: 0px 0px 0px 10px;
  margin-left: -10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0s;
  -moz-transition: all 0s;
  -o-transition: all 0s;
  transition: all 0s;
}
.pulldown-menu .menu-item-box .menu-item dl dd .second-level-link a {
  position: relative;
  padding-left: 25px;
  margin-top: 2px;
}
.pulldown-menu .menu-item-box .menu-item dl dd .second-level-link a i {
  position: absolute;
  left: 10px;
  top: 17px;
  width: 5px;
  height: 5px;
  background: url(../images/index_sprite_img.png) no-repeat -444px -178px;
  margin: 0px !important;
}
.pulldown-menu .menu-item-box .menu-item dl dd .second-level-link a span {
  opacity: 0.3;
}
.pulldown-menu .menu-item-box .menu-item dl dd a span {
  opacity: 0.5;
}
.pulldown-menu .menu-item-box .menu-item dl dd a:hover {
  color: #FF6666;
  background-color: #37404D;
}
.pulldown-menu .menu-item-box .menu-item dl dd a:hover span {
  opacity: 1;
}
.pulldown-menu .menu-item-box .menu-item dl dd a i {
  font-size: 12px;
  color: #FF6666;
  font-style: normal;
  margin-left: 10px;
  opacity: 1;
}
.hotlink {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 100px;
  padding: 32px 0px;
  background-color: #303742;
}
.hotlink .hotlink-box {
  position: relative;
  width: 1200px;
  height: 100%;
  padding-left: 53px;
  margin: 0 auto;
}
.hotlink .hotlink-box span.hot {
  left: 0px;
  top: 8px;
}
.hotlink .hotlink-box > ul {
  height: 100%;
}
.hotlink .hotlink-box > ul > li {
  position: relative;
  float: left;
  height: 100%;
  margin-right: 9px;
}
.hotlink .hotlink-box > ul > li.last {
  margin-right: 0px;
}
.hotlink .hotlink-box > ul > li a {
  display: block;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  color: #FFF;
  padding: 0px 21px;
  -webkit-border-radius: 18px;
  -moz-border-radius: 18px;
  -o-border-radius: 18px;
  border-radius: 18px;
  -webkit-transition: all 0s;
  -moz-transition: all 0s;
  -o-transition: all 0s;
  transition: all 0s;
}
.hotlink .hotlink-box > ul > li a:hover {
  background: #39424F;
}
.hotlink .hotlink-box > ul > li a span {
  font-size: 14px;
  color: #FFFEFE;
  opacity: 0.5;
}
.hotlink .hotlink-box > ul > li a i {
  font-size: 12px;
  color: #FF6666;
  margin-left: 10px;
}
.hotlink .hotlink-box > ul > li a:hover span {
  color: #FF6666;
  opacity: 1;
}
i {
  font-style: normal;
}
span.hot {
  position: absolute;
  width: 44px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  color: #FF6666;
  text-align: center;
  background: url(../images/hot_sprite.png) no-repeat center;
  padding-right: 6px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
/*======= common nav end =======*/
.aboutlayer,
.qq_phone,
.newslayer,
.zpxq,
.video_list {
  position: relative;
}
.fixed_nav {
  border-top: 3px solid #FF6666;
  position: absolute;
  left: 220px;
  top: 77px;
  width: 100px;
  background-color: #fff;
  padding: 0px 15px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  z-index: 999;
}
.fixed_nav > a {
  position: relative;
  display: block;
  width: 100%;
  height: 100px;
  line-height: 48px;
  font-size: 16px;
  color: #5E6D81;
  text-align: center;
  padding-top: 52px;
  border-bottom: 1px solid #F0F2F5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.fixed_nav > a:last-child {
  border-bottom: none;
}
.fixed_nav > a span {
  position: absolute;
  left: 50%;
  bottom: 48px;
}
.fixed_nav > a:hover,
.fixed_nav > a.active {
  color: #FF6666;
}
.fixed_nav > a.page_us span {
  width: 40px;
  height: 30px;
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -272px -220px;
  margin-left: -20px;
}
.fixed_nav > a.page_us:hover span,
.fixed_nav > a.page_us.active span {
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -450px -220px;
}
.fixed_nav > a.page_news span {
  width: 32px;
  height: 34px;
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -322px -220px;
  margin-left: -16px;
}
.fixed_nav > a.page_news:hover span,
.fixed_nav > a.page_news.active span {
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -500px -220px;
}
.fixed_nav > a.page_zhaopin span {
  width: 34px;
  height: 30px;
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -364px -220px;
  margin-left: -17px;
}
.fixed_nav > a.page_zhaopin:hover span,
.fixed_nav > a.page_zhaopin.active span {
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -542px -220px;
}
.fixed_nav > a.page_client span {
  width: 32px;
  height: 32px;
  background: url(../images/float_nav_img.png) -117px 0px;
  margin-left: -16px;
}
.fixed_nav > a.page_client:hover span,
.fixed_nav > a.page_client.active span {
  background: url(../images/float_nav_img.png) -117px -38px;
}
.fixed_nav > a.page_contact span {
  width: 32px;
  height: 32px;
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -408px -220px;
  margin-left: -16px;
}
.fixed_nav > a.page_contact:hover span,
.fixed_nav > a.page_contact.active span {
  background: url(../images/yisu-aboutus-sprite.png) no-repeat -586px -220px;
}
/*======= common nav end =======*/
/*======= common banner start =======*/
.banner {
  position: relative;
  width: 100%;
  height: 480px;
  background: #1E232D;
}
.banner .banner-center {
  position: relative;
  width: 1200px;
  height: 100%;
  margin: 0 auto;
}
.banner .banner-center .banner-text {
  padding-top: 140px;
}
.banner .banner-center .banner-text h1 {
  line-height: 76px;
  font-size: 36px;
  color: #FFFFFF;
}
.banner .banner-center .banner-text p {
  max-width: 532px;
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.5;
}
.banner .banner-center .enterbtn {
  display: inline-block;
  margin-top: 30px;
  vertical-align: top;
}
.banner .banner-center .enterbtn a {
  float: left;
  width: 130px;
  height: 36px;
  line-height: 34px;
  text-align: center;
  font-size: 14px;
  color: #FF6666;
  border: 1px solid #FF6666;
  margin-left: 20px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.banner .banner-center .enterbtn a.ml-0 {
  margin-left: 0px;
}
.banner .banner-center .enterbtn a.turnlink {
  color: #FFF;
  background-color: #FF6666;
}
.banner .banner-center .enterbtn a.turnlink:hover {
  background-color: #FF4444;
  border-color: #FF4444;
}
.banner .banner-center .yisu-product-link {
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  padding-top: 15px;
}
.banner .banner-center .yisu-product-link span,
.banner .banner-center .yisu-product-link a {
  color: #FF6666;
}
.banner .banner-center .yisu-product-link a {
  margin-left: 10px;
}
.banner .banner-center .yisu-product-link a:hover {
  text-decoration: underline;
}
.banner .banner-center .banner-right-img {
  position: absolute;
  top: 90px;
  right: 70px;
  width: 460px;
  height: 330px;
}
.banner .server-nav {
  position: absolute;
  left: 0px;
  top: 402px;
  width: 100%;
  height: 78px;
  background-color: transparent;
  z-index: 998;
}
.banner .serverkind-box {
  position: relative;
  width: 201px;
  height: 78px;
  line-height: 78px;
  font-size: 17px;
  color: #ffffff;
  padding-left: 20px;
  border-right: 1px solid #42464c;
  border-color: hsla(0, 0%, 100%, 0.06);
  display: none;
}
.banner .serverkind-box:hover {
  color: #FF6666;
  cursor: pointer;
}
.banner .serverkind-box i {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-style: normal;
  background: url("../images/cloud_sprite_img.png") no-repeat -550px -10px;
  margin-right: 18px;
  vertical-align: middle;
}
.banner .serverkind-box:hover i {
  background: url("../images/cloud_sprite_img.png") no-repeat -550px -36px;
}
.banner .server-option {
  position: absolute;
  right: 1px;
  top: 66px;
  display: none;
}
.banner .server-option ul li a {
  display: block;
  width: 146px;
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: #ffffff;
  background-color: #394046;
  padding-left: 54px;
}
.banner .server-option ul li a:hover {
  background-color: #ff6765;
}
.banner .server-navbox {
  width: 1200px;
  margin: 0 auto;
}
.banner .server-navbox > div {
  float: left;
}
.banner .server-navbox .server-bottomnav {
  width: 821px;
  margin-left: 253px;
}
.banner .server-bottomnav ul li {
  float: left;
  margin-right: 40px;
}
.banner .server-bottomnav ul li:last-child {
  margin-right: 0px;
}
.banner .server-bottomnav ul li a {
  display: block;
  padding: 0px 22px;
  height: 75px;
  line-height: 78px;
  font-size: 17px;
  color: #ffffff;
}
.banner .server-bottomnav ul li.activenav a,
.banner .server-bottomnav ul li a:hover {
  color: #FF6666;
  border-bottom: 3px solid #FF6666;
}
.banner .server-navbox > div.nowbuy {
  float: right;
  margin-top: 21px;
  display: none;
}
.banner .nowbuy a {
  display: block;
  width: 100px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #ffffff;
  background-color: #FF6666;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.banner .entrance {
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  padding-top: 20px;
}
/*======= common banner end =======*/
/*======= common normal user example start =======*/
.cloudserveruser {
  width: 100%;
  height: 760px;
  background: url("../images/clouduser.jpg") no-repeat center;
  padding-top: 100px;
}
.cloudserveruser .user-title {
  height: 110px;
  line-height: 36px;
  font-size: 36px;
  color: #ffffff;
  text-align: center;
}
.cloudserveruser .centershow {
  width: 1200px;
  margin: 0 auto;
}
.cloudserveruser .centershow .center-picture {
  position: relative;
  width: 152px;
  height: 152px;
  margin: 0 auto;
  background-color: #fff;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.cloudserveruser .centershow .center-picture::before {
  position: absolute;
  left: -11px;
  top: -11px;
  content: '';
  width: 172px;
  height: 172px;
  border: 1px solid #ffffff;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.cloudserveruser .centershow .center-picture img {
  width: 100%;
  height: 100%;
}
.cloudserveruser .centershow .picture-name {
  height: 57px;
  line-height: 22px;
  font-size: 22px;
  color: #ffffff;
  text-align: center;
  margin-top: 46px;
}
.cloudserveruser .centershow .yisu-text {
  height: 44px;
  line-height: 18px;
  font-size: 18px;
  color: #B1B1BA;
  text-align: center;
}
.cloudserveruser .centershow .user-video {
  height: 68px;
  line-height: 14px;
  font-size: 14px;
  text-align: center;
}
.cloudserveruser .centershow .user-video a {
  color: #FF6666;
}
.cloudserveruser .centershow .user-list {
  text-align: center;
}
.cloudserveruser .centershow .user-list a {
  position: relative;
  display: inline-block;
  width: 70px;
  height: 70px;
  background-color: #fff;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin-right: 84px;
}
.cloudserveruser .centershow .user-list a span {
  position: absolute;
  left: 0;
  top: 0;
  width: 70px;
  height: 70px;
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  z-index: 10;
  display: none;
}
.cloudserveruser .centershow .user-list a span i {
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0px 0px -20px;
  width: 40px;
  height: 40px;
  background: url(../images/cloud_sprite_img.png) no-repeat -550px -152px;
}
.cloudserveruser .centershow .user-list a:hover span {
  display: block;
}
.cloudserveruser .centershow .user-list a:last-child {
  margin-right: 0px;
}
.cloudserveruser .centershow .user-list a img {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}
.cloudserveruser .centershow .user-list a.select-user img,
.cloudserveruser .centershow .user-list a:hover img {
  opacity: 1;
}
.cloudserveruser .centershow .user-list a.select-user::before {
  position: absolute;
  left: -5px;
  top: -5px;
  content: '';
  width: 78px;
  height: 78px;
  border: 1px solid #f96868;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
/*======= common normal user example end =======*/
/*======= common lunbo user example start =======*/
.yisu-partner {
  width: 100%;
  background: #1E233C;
  padding-top: 90px;
}
.yisu-partner.hk-yisu-partner {
  background: #1E233C url(../images/hk-partner-bg.jpg) no-repeat center;
}
.yisu-partner .partner-msg {
  width: 1200px;
  overflow: hidden;
  margin: 0 auto;
}
.yisu-partner .partner-msg .partner-title {
  height: 78px;
  line-height: 36px;
  font-size: 36px;
  color: #ffffff;
  text-align: center;
}
.yisu-partner .partner-msg .partner-need {
  line-height: 24px;
  font-size: 18px;
  color: #A7A8B2;
  text-align: center;
  padding-bottom: 84px;
}
.yisu-partner .partner-msg .swiper-container {
  padding-top: 0px;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide {
  float: left;
  width: 240px;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des {
  float: left;
  color: #666578;
  text-align: center;
  cursor: pointer;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -ms-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic {
  position: relative;
  width: 154px;
  height: 154px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin: 0px 43px 47px;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -ms-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic > img {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic .play {
  position: absolute;
  left: 0;
  top: 0;
  width: 154px;
  height: 154px;
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  z-index: 10;
  display: none;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic .play i {
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -30px 0px 0px -30px;
  width: 60px;
  height: 60px;
  background: url(../images/clients_sprite_img.png) no-repeat -10px -10px;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic:hover .play {
  display: block;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic:hover .head_picture {
  visibility: visible;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic .head_picture {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background: #1E233C;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  visibility: hidden;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-showpic .head_picture img {
  width: 100%;
  height: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-name {
  height: 26px;
  line-height: 16px;
  font-size: 16px;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide .partner-des .partner-company {
  height: 34px;
  line-height: 22px;
  font-size: 16px;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide.swiper-slide-active div {
  color: #ffffff;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide.swiper-slide-active .partner-showpic > img {
  opacity: 1;
}
.yisu-partner .partner-msg .swiper-container .swiper-slide.swiper-slide-active .partner-showpic span.play {
  display: block;
}
.yisu-partner .partner-msg .look-video {
  margin-top: 82px;
  text-align: center;
  padding-bottom: 40px;
}
.yisu-partner .partner-msg .look-video a {
  font-size: 16px;
  color: #FF6666;
}
/*======= common lunbo user example end =======*/
/*======= common help-center start =======*/
.yisuhelp {
  width: 100%;
  background-color: #fff;
  padding-top: 100px;
  padding-bottom: 93px;
}
.help-list {
  width: 1200px;
  margin: 0 auto;
}
.help-title {
  height: 126px;
  line-height: 36px;
  font-size: 36px;
  text-align: center;
  color: #2D3037;
}
.helpbox {
  float: left;
  width: 380px;
  height: 279px;
  padding: 0px 20px;
  margin-right: 30px;
  background-color: #fff;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  padding-bottom: 30px;
}
.htlp-kind .helpbox.last {
  margin-right: 0px;
}
.helpbox-head {
  height: 70px;
  line-height: 70px;
  border-bottom: 1px solid #DBE1E8;
}
.helpbox-head h4 {
  float: left;
  width: 100px;
  height: 67px;
  line-height: 67px;
  font-size: 20px;
  text-align: center;
  color: #FF6666;
  border-bottom: 3px solid #FF6666;
}
.helpbox-head a {
  float: right;
  font-size: 14px;
  color: #9EA7B3;
}
.helpbox a:hover {
  color: #FF6666;
}
.help-link {
  padding: 19px 0px 0px 24px;
}
.help-link a {
  position: relative;
  line-height: 40px;
  font-size: 16px;
  color: #2D3037;
}
.help-link a.hot-link {
  color: #FF6666;
}
.help-link a::before {
  position: absolute;
  left: -11px;
  top: 10px;
  content: '';
  width: 2px;
  height: 2px;
  background-color: #2D3037;
}
/*======= common help-center end =======*/
/*======= 公共 footer start =======*/
.footer {
  width: 100%;
  background-color: #323A45;
  padding-top: 70px;
}
.other-link {
  width: 1200px;
  margin: 0 auto;
  min-height: 360px;
}
.other-link > div {
  float: left;
}
.other-link .yisu-contact {
  float: right;
}
.link-look {
  width: 960px;
  padding-bottom: 45px;
}
.link-list {
  float: left;
  width: 20%;
}
.link-list .link-title {
  height: 45px;
  line-height: 20px;
  font-size: 20px;
  color: #ffffff;
}
.link-list ul li a {
  line-height: 33px;
  color: #ABACAF;
  font-size: 15px;
}
.link-list ul li a:hover {
  color: #FF6666;
}
.yisu-contact {
  width: 240px;
}
.bottom-logo {
  width: 132px;
  height: 38px;
}
.bottom-logo a {
  display: block;
  width: 100%;
  height: 100%;
  background: url("../images/index_sprite_img.png") no-repeat -151px -10px;
}
.yisu-companyname {
  height: 40px;
  line-height: 18px;
  font-size: 18px;
  color: #ffffff;
  margin-top: 25px;
}
.yisu-phone,
.yisu-qq {
  height: 34px;
  line-height: 15px;
  font-size: 15px;
  color: #ABACAF;
}
.erweima-box {
  margin-top: 34px;
}
.erweima-box > div {
  float: left;
  width: 100px;
}
.erweima-box > div:first-child {
  margin-right: 30px;
}
.wechat-erwei div:first-child,
.phonenet-erwei div:first-child {
  height: 30px;
  line-height: 14px;
  font-size: 14px;
  color: #ABACAF;
}
.yisu_wechat,
.yisu_web {
  width: 100%;
  height: 100px;
}
.yisu_wechat {
  background: url("../images/index_sprite_img.png") no-repeat -250px -206px;
}
.yisu_web {
  background: url("../images/index_sprite_img.png") no-repeat -360px -206px;
}
.rightBar .first_ic {
  background: #222 url("../images/index_sprite_img.png") no-repeat -10px -206px;
}
.rightBar .second_ic {
  background: #222 url("../images/index_sprite_img.png") no-repeat -70px -206px;
}
.rightBar .third_ic {
  background: #222 url("../images/index_sprite_img.png") no-repeat -130px -206px;
}
.rightBar .fourth_ic {
  background: #222 url("../images/index_sprite_img.png") no-repeat -190px -206px;
}
.footer-bottom {
  height: 100px;
  text-align: center;
  padding-top: 27px;
  border-top: 1px solid #42444B;
}
.footer-bottom p {
  line-height: 26px;
  font-size: 14px;
  color: #6C6E73;
}
.footer-bottom p span {
  margin-left: 17px;
}
.footer-bottom p span.police-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("../images/index_sprite_img.png") no-repeat -334px -170px;
  vertical-align: top;
}
/*======= 公共 footer end =======*/
.database-specialty {
  width: 100%;
  background-color: #fff;
  padding: 85px 0px 42px;
}
.database-specialty .content {
  width: 1200px;
  margin: 0 auto;
}
.database-specialty .content > h2 {
  line-height: 46px;
  font-size: 36px;
  font-weight: 400;
  color: #2D3037;
  text-align: center;
}
.database-specialty .content .title-des {
  line-height: 28px;
  font-size: 18px;
  color: #5E6D81;
  text-align: center;
  margin-top: 30px;
}
.database-specialty .content .database-item {
  margin-top: 70px;
}
.database-specialty .content .database-item .item {
  position: relative;
  float: left;
  height: 140px;
  width: 580px;
  height: 146px;
  padding-left: 120px;
  margin: 0px 40px 32px 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.database-specialty .content .database-item .item.mr-0 {
  margin-right: 0px;
}
.database-specialty .content .database-item .item.mb-0 {
  margin-bottom: 0px;
}
.database-specialty .content .database-item .item .bgimg {
  position: absolute;
  left: 0px;
  top: 14px;
  width: 80px;
  height: 80px;
}
.database-specialty .content .database-item .item.one .bgimg {
  background: url(../images/database-sprite.png) no-repeat 0px -360px;
}
.database-specialty .content .database-item .item.one:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat 0px -445px;
}
.database-specialty .content .database-item .item.two .bgimg {
  background: url(../images/database-sprite.png) no-repeat -85px -360px;
}
.database-specialty .content .database-item .item.two:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -85px -445px;
}
.database-specialty .content .database-item .item.three .bgimg {
  background: url(../images/database-sprite.png) no-repeat -170px -360px;
}
.database-specialty .content .database-item .item.three:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -170px -445px;
}
.database-specialty .content .database-item .item.four .bgimg {
  background: url(../images/database-sprite.png) no-repeat -255px -360px;
}
.database-specialty .content .database-item .item.four:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -255px -445px;
}
.database-specialty .content .database-item .item.five .bgimg {
  background: url(../images/database-sprite.png) no-repeat -360px -360px;
}
.database-specialty .content .database-item .item.five:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -360px -445px;
}
.database-specialty .content .database-item .item.six .bgimg {
  background: url(../images/database-sprite.png) no-repeat -445px -360px;
}
.database-specialty .content .database-item .item.six:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -445px -445px;
}
.database-specialty .content .database-item .item.seven .bgimg {
  background: url(../images/database-sprite.png) no-repeat -530px -360px;
}
.database-specialty .content .database-item .item.seven:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -530px -445px;
}
.database-specialty .content .database-item .item.eight .bgimg {
  background: url(../images/database-sprite.png) no-repeat -615px -360px;
}
.database-specialty .content .database-item .item.eight:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -615px -445px;
}
.database-specialty .content .database-item .item.nine .bgimg {
  background: url(../images/database-sprite.png) no-repeat -360px 0px;
}
.database-specialty .content .database-item .item.nine:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -360px -85px;
}
.database-specialty .content .database-item .item.ten .bgimg {
  background: url(../images/database-sprite.png) no-repeat -445px 0px;
}
.database-specialty .content .database-item .item.ten:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -445px -85px;
}
.database-specialty .content .database-item .item.eleven .bgimg {
  background: url(../images/database-sprite.png) no-repeat -530px 0px;
}
.database-specialty .content .database-item .item.eleven:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -530px -85px;
}
.database-specialty .content .database-item .item.twelve .bgimg {
  background: url(../images/database-sprite.png) no-repeat -615px 0px;
}
.database-specialty .content .database-item .item.twelve:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -615px -85px;
}
.database-specialty .content .database-item .item.sql-one .bgimg {
  background: url(../images/database-sprite.png) no-repeat 0px 0px;
}
.database-specialty .content .database-item .item.sql-one:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat 0px -85px;
}
.database-specialty .content .database-item .item.sql-two .bgimg {
  background: url(../images/database-sprite.png) no-repeat -85px 0px;
}
.database-specialty .content .database-item .item.sql-two:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -85px -85px;
}
.database-specialty .content .database-item .item.sql-three .bgimg {
  background: url(../images/database-sprite.png) no-repeat -170px 0px;
}
.database-specialty .content .database-item .item.sql-three:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -170px -85px;
}
.database-specialty .content .database-item .item.sql-four .bgimg {
  background: url(../images/database-sprite.png) no-repeat -255px 0px;
}
.database-specialty .content .database-item .item.sql-four:hover .bgimg {
  background: url(../images/database-sprite.png) no-repeat -255px -85px;
}
.database-specialty .content .database-item .item.slb-one .bgimg {
  background: url(../images/slb-msg.png) no-repeat 0px 0px;
}
.database-specialty .content .database-item .item.slb-one:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat 0px -85px;
}
.database-specialty .content .database-item .item.slb-two .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px 0px;
}
.database-specialty .content .database-item .item.slb-two:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px -85px;
}
.database-specialty .content .database-item .item.slb-three .bgimg {
  background: url(../images/slb-msg.png) no-repeat -170px 0px;
}
.database-specialty .content .database-item .item.slb-three:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -170px -85px;
}
.database-specialty .content .database-item .item.slb-four .bgimg {
  background: url(../images/slb-msg.png) no-repeat -255px 0px;
}
.database-specialty .content .database-item .item.slb-four:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -255px -85px;
}
.database-specialty .content .database-item .item.cdn-one .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px -190px;
}
.database-specialty .content .database-item .item.cdn-one:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px -275px;
}
.database-specialty .content .database-item .item.cdn-two .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px -190px;
}
.database-specialty .content .database-item .item.cdn-two:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px -275px;
}
.database-specialty .content .database-item .item.cdn-three .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px -190px;
}
.database-specialty .content .database-item .item.cdn-three:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px -275px;
}
.database-specialty .content .database-item .item.cdn-four .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px -190px;
}
.database-specialty .content .database-item .item.cdn-four:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px -275px;
}
.database-specialty .content .database-item .item.cdn-five .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px -190px;
}
.database-specialty .content .database-item .item.cdn-five:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px -275px;
}
.database-specialty .content .database-item .item.cdn-six .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px -190px;
}
.database-specialty .content .database-item .item.cdn-six:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px -275px;
}
.database-specialty .content .database-item .item.msg-one .bgimg {
  background: url(../images/slb-msg.png) no-repeat 0px -360px;
}
.database-specialty .content .database-item .item.msg-one:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat 0px -445px;
}
.database-specialty .content .database-item .item.msg-two .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px -360px;
}
.database-specialty .content .database-item .item.msg-two:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px -445px;
}
.database-specialty .content .database-item .item.msg-three .bgimg {
  background: url(../images/slb-msg.png) no-repeat -170px -360px;
}
.database-specialty .content .database-item .item.msg-three:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -170px -445px;
}
.database-specialty .content .database-item .item.msg-four .bgimg {
  background: url(../images/slb-msg.png) no-repeat -255px -360px;
}
.database-specialty .content .database-item .item.msg-four:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -255px -445px;
}
.database-specialty .content .database-item .item.dns-one .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px 0px;
}
.database-specialty .content .database-item .item.dns-one:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px -85px;
}
.database-specialty .content .database-item .item.dns-two .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px 0px;
}
.database-specialty .content .database-item .item.dns-two:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px -85px;
}
.database-specialty .content .database-item .item.dns-three .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px 0px;
}
.database-specialty .content .database-item .item.dns-three:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px -85px;
}
.database-specialty .content .database-item .item.dns-four .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px 0px;
}
.database-specialty .content .database-item .item.dns-four:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px -85px;
}
.database-specialty .content .database-item .item.dns-five .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px 0px;
}
.database-specialty .content .database-item .item.dns-five:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px -85px;
}
.database-specialty .content .database-item .item.dns-six .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px 0px;
}
.database-specialty .content .database-item .item.dns-six:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px -85px;
}
.database-specialty .content .database-item .item h3 {
  line-height: 48px;
  font-size: 20px;
  font-weight: normal;
  color: #2D3037;
}
.database-specialty .content .database-item .item p {
  line-height: 28px;
  font-size: 16px;
  color: #5E6D81;
}
.product-ability {
  width: 100%;
  background-color: #f0f2f5;
  padding: 100px 0px 60px;
}
.product-content {
  width: 1200px;
  margin: 0 auto;
}
.product-title {
  height: 134px;
  line-height: 35px;
  font-size: 35px;
  color: #2d3037;
  text-align: center;
}
.ability-kinds > div {
  float: left;
  width: 280px;
  height: 310px;
  background-color: #fff;
  transition: all 0.4s;
  padding-top: 49px;
  margin-right: 26px;
  -webkit-box-shadow: 0px 0px 20px 3px #eaeced;
  -moz-box-shadow: 0px 0px 20px 3px #eaeced;
  box-shadow: 0px 0px 20px 3px #eaeced;
}
.ability-kinds > div .left-picture {
  width: 80px;
  height: 80px;
  margin: 0 auto;
}
.ability-kinds > div .right-content {
  margin-top: 34px;
  text-align: center;
}
.ability-kinds > div .right-content .content-title {
  height: 36px;
  line-height: 20px;
  font-size: 20px;
  color: #2d3037;
}
.ability-kinds > div .right-content .content-text p {
  line-height: 28px;
  font-size: 16px;
  color: #5e6d81;
}
.ability-kinds > div.mr-0 {
  margin-right: 0px;
}
.ability-kinds .one .left-picture {
  background: url(../images/database-sprite.png) no-repeat 0px -530px;
}
.ability-kinds .one:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat 0px -615px;
}
.ability-kinds .two .left-picture {
  background: url(../images/database-sprite.png) no-repeat -85px -530px;
}
.ability-kinds .two:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -85px -615px;
}
.ability-kinds .three .left-picture {
  background: url(../images/database-sprite.png) no-repeat -170px -530px;
}
.ability-kinds .three:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -170px -615px;
}
.ability-kinds .four .left-picture {
  background: url(../images/database-sprite.png) no-repeat -255px -530px;
}
.ability-kinds .four:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -255px -615px;
}
.ability-kinds .five .left-picture {
  background: url(../images/database-sprite.png) no-repeat -360px -530px;
}
.ability-kinds .five:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -360px -615px;
}
.ability-kinds .six .left-picture {
  background: url(../images/database-sprite.png) no-repeat -445px -530px;
}
.ability-kinds .six:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -445px -615px;
}
.ability-kinds .seven .left-picture {
  background: url(../images/database-sprite.png) no-repeat -530px -530px;
}
.ability-kinds .seven:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -530px -615px;
}
.ability-kinds .eight .left-picture {
  background: url(../images/database-sprite.png) no-repeat -615px -530px;
}
.ability-kinds .eight:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -615px -615px;
}
.ability-kinds .nine .left-picture {
  background: url(../images/database-sprite.png) no-repeat -360px -170px;
}
.ability-kinds .nine:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -360px -255px;
}
.ability-kinds .ten .left-picture {
  background: url(../images/database-sprite.png) no-repeat -445px -170px;
}
.ability-kinds .ten:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -445px -255px;
}
.ability-kinds .eleven .left-picture {
  background: url(../images/database-sprite.png) no-repeat -530px -170px;
}
.ability-kinds .eleven:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -530px -255px;
}
.ability-kinds .twelve .left-picture {
  background: url(../images/database-sprite.png) no-repeat -615px -170px;
}
.ability-kinds .twelve:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -615px -255px;
}
.ability-kinds .sql-one .left-picture {
  background: url(../images/database-sprite.png) no-repeat 0px -170px;
}
.ability-kinds .sql-one:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat 0px -255px;
}
.ability-kinds .sql-two .left-picture {
  background: url(../images/database-sprite.png) no-repeat -85px -170px;
}
.ability-kinds .sql-two:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -85px -255px;
}
.ability-kinds .sql-three .left-picture {
  background: url(../images/database-sprite.png) no-repeat -170px -170px;
}
.ability-kinds .sql-three:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -170px -255px;
}
.ability-kinds .sql-four .left-picture {
  background: url(../images/database-sprite.png) no-repeat -255px -170px;
}
.ability-kinds .sql-four:hover .left-picture {
  background: url(../images/database-sprite.png) no-repeat -255px -255px;
}
.ability-kinds .slb-one .left-picture {
  background: url(../images/slb-msg.png) no-repeat 0px -170px;
}
.ability-kinds .slb-one:hover .left-picture {
  background: url(../images/slb-msg.png) no-repeat 0px -255px;
}
.ability-kinds .slb-two .left-picture {
  background: url(../images/slb-msg.png) no-repeat -85px -170px;
}
.ability-kinds .slb-two:hover .left-picture {
  background: url(../images/slb-msg.png) no-repeat -85px -255px;
}
.ability-kinds .slb-three .left-picture {
  background: url(../images/slb-msg.png) no-repeat -170px -170px;
}
.ability-kinds .slb-three:hover .left-picture {
  background: url(../images/slb-msg.png) no-repeat -170px -255px;
}
.ability-kinds .slb-four .left-picture {
  background: url(../images/slb-msg.png) no-repeat -255px -170px;
}
.ability-kinds .slb-four:hover .left-picture {
  background: url(../images/slb-msg.png) no-repeat -255px -255px;
}
/*========= 水平（内容从左到右）功能模块  start ============*/
.row-ability {
  width: 100%;
  background-color: #F0F2F5;
  padding-bottom: 85px;
}
.row-ability .content {
  width: 1200px;
  margin: 0 auto;
}
.row-ability .content h2 {
  line-height: 216px;
  font-size: 36px;
  color: #2D3037;
  text-align: center;
}
.row-ability .row-item .item {
  position: relative;
  float: left;
  width: 590px;
  height: 170px;
  background-color: #fff;
  padding: 15px 60px 0px 160px;
  margin: 0px 20px 20px 0px;
}
.row-ability .row-item .item.mr-0 {
  margin-right: 0px;
}
.row-ability .row-item .item h3 {
  line-height: 48px;
  font-size: 20px;
  color: #2D3037;
}
.row-ability .row-item .item p {
  line-height: 28px;
  font-size: 16px;
  color: #5E6D81;
}
.row-ability .row-item .item .bgimg {
  position: absolute;
  left: 40px;
  top: 30px;
  width: 80px;
  height: 80px;
}
.row-ability .row-item .item.cdn-one .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px -360px;
}
.row-ability .row-item .item.cdn-one:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat 0px -445px;
}
.row-ability .row-item .item.cdn-two .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px -360px;
}
.row-ability .row-item .item.cdn-two:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -85px -445px;
}
.row-ability .row-item .item.cdn-three .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px -360px;
}
.row-ability .row-item .item.cdn-three:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -170px -445px;
}
.row-ability .row-item .item.cdn-four .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px -360px;
}
.row-ability .row-item .item.cdn-four:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -255px -445px;
}
.row-ability .row-item .item.cdn-five .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px -360px;
}
.row-ability .row-item .item.cdn-five:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -340px -445px;
}
.row-ability .row-item .item.cdn-six .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px -360px;
}
.row-ability .row-item .item.cdn-six:hover .bgimg {
  background: url(../images/cdn-sprite.png) no-repeat -425px -445px;
}
/*========= 水平（内容从左到右）功能模块  end ============*/
.application-scene {
  width: 100%;
  background-color: #F0F2F5;
  padding-top: 85px;
}
.application-scene .content {
  width: 1200px;
  margin: 0 auto;
}
.application-scene .content h2 {
  line-height: 46px;
  font-size: 36px;
  font-weight: 400;
  color: #2D3037;
  text-align: center;
}
.application-scene .content .tab {
  text-align: center;
  margin-top: 50px;
}
.application-scene .content .tab .tab-list {
  position: relative;
  display: inline-block;
  height: 78px;
  vertical-align: top;
}
.application-scene .content .tab .tab-list ul {
  height: 78px;
}
.application-scene .content .tab .tab-list ul li {
  float: left;
  width: 220px;
  height: 78px;
  line-height: 78px;
  font-size: 18px;
  color: #5E6D81;
  margin-right: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}
.application-scene .content .tab .tab-list ul li.last {
  margin-right: 0px;
}
.application-scene .content .tab .tab-list ul li.active {
  color: #FF6666;
}
.application-scene .content .tab .tab-list .tab-line {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 220px;
  height: 3px;
  background-color: #ff6666;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -ms-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
}
.application-scene .content .tab .tab-list .tab-line::before {
  position: absolute;
  left: 50%;
  top: -12px;
  content: '';
  width: 0px;
  height: 0px;
  border: 6px solid #FF6666;
  border-top-color: transparent;
  border-right-color: transparent;
  border-left-color: transparent;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
.application-scene .content .message-item {
  width: 100%;
  height: 520px;
  background-color: #fff;
}
.application-scene .content .message-item .item {
  height: 520px;
  display: none;
}
.application-scene .content .message-item .item.show {
  display: block;
}
.application-scene .content .message-item .item > div {
  float: left;
  height: 100%;
}
.application-scene .content .message-item .item .left {
  width: 820px;
  padding: 60px 51px 0px;
}
.application-scene .content .message-item .item .right {
  width: 380px;
  padding: 46px 50px 0px;
  border-left: 1px solid #F0F2F5;
}
.application-scene .content .message-item .item .right h3 {
  line-height: 48px;
  font-size: 20px;
  color: #2D3037;
}
.application-scene .content .message-item .item .right p {
  line-height: 26px;
  font-size: 14px;
  color: #5E6D81;
  margin-bottom: 20px;
}
.application-scene .content .message-item .item .right a {
  display: inline-block;
  width: 140px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  color: #FFF;
  background-color: #FF6666;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  margin-top: 18px;
}
.application-scene .content .message-item .item .right a:hover {
  background-color: #FF4444;
}
/*========== common yisu-specialty start =========*/
.yisu-specialty {
  width: 100%;
  background-color: #fff;
  padding-top: 100px;
}
.specialty-title {
  width: 100%;
  height: 144px;
  line-height: 34px;
  font-size: 34px;
  color: #2D3037;
  text-align: center;
}
.specialty-show {
  width: 1200px;
  margin: 0 auto;
}
.specialtybox {
  float: left;
  width: 400px;
  height: 234px;
  text-align: center;
  margin-bottom: 78px;
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -ms-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}
.specialtybox-head {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-bottom: 33px;
}
.one-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -10px -10px;
}
.one-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -10px -100px;
}
.two-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -100px -10px;
}
.two-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -100px -100px;
}
.three-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -190px -10px;
}
.three-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -190px -100px;
}
.four-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -280px -10px;
}
.four-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -280px -100px;
}
.five-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -370px -100px;
}
.five-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -370px -100px;
}
.six-specialtybox .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -460px -10px;
}
.six-specialtybox:hover .specialtybox-head {
  background: url("../images/cloud_sprite_img.png") no-repeat -460px -100px;
}
.specialtybox-title {
  height: 34px;
  line-height: 20px;
  font-size: 20px;
  color: #2D3037;
  text-align: center;
}
.specialtybox-des p {
  line-height: 28px;
  font-size: 16px;
  color: #5E6D81;
}
/*====================  物理服务器 亿速特点  =======================*/
.first-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -10px -10px;
}
.first-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -10px -100px;
}
.second-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -100px -10px;
}
.second-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -100px -100px;
}
.third-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -190px -10px;
}
.third-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -190px -100px;
}
.fourth-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -280px -10px;
}
.fourth-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -280px -100px;
}
.fifth-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -370px -10px;
}
.fifth-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -370px -100px;
}
.sixth-specialtybox .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -460px -10px;
}
.sixth-specialtybox:hover .specialtybox-head {
  background: url("../images/physice_sprite_img.png") no-repeat -460px -100px;
}
/*=================  专用宿主机 亿速特点  ==============================*/
.frame-one-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -10px -10px;
}
.frame-one-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -10px -101px;
}
.frame-two-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -96px -10px;
}
.frame-two-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -96px -101px;
}
.frame-three-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -182px -10px;
}
.frame-three-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -182px -101px;
}
.frame-four-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -268px -10px;
}
.frame-four-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -268px -101px;
}
.frame-five-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -354px -10px;
}
.frame-five-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -354px -101px;
}
.frame-six-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -440px -10px;
}
.frame-six-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -440px -101px;
}
.seven-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -10px -193px;
}
.seven-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -10px -283px;
}
.eight-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -96px -193px;
}
.eight-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -96px -283px;
}
.nine-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -182px -193px;
}
.nine-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -182px -283px;
}
.ten-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -272px -193px;
}
.ten-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -272px -283px;
}
.eleve-specialtybox .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -358px -193px;
}
.eleve-specialtybox:hover .specialtybox-head {
  background: url("../images/mainframe_sprite_img.png") no-repeat -358px -283px;
}
.ten-specialtybox {
  margin-left: 200px;
}
.eleve-specialtybox {
  margin-right: 200px;
}
/*=================  机柜托管 亿速特点  ==============================*/
.trustee-specialty .specialtybox {
  width: 325px;
  height: 300px;
  margin-right: 112px;
}
.specialty-show .trustee-one-specialtybox .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -10px -10px;
}
.specialty-show .trustee-one-specialtybox:hover .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -10px -100px;
}
.specialty-show .trustee-two-specialtybox .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -100px -10px;
}
.specialty-show .trustee-two-specialtybox:hover .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -100px -100px;
}
.specialty-show .trustee-three-specialtybox .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -190px -10px;
}
.specialty-show .trustee-three-specialtybox:hover .specialtybox-head {
  background: url("../images/trustee_sprite_img.png") no-repeat -190px -100px;
}
.specialty-show .trustee-three-specialtybox {
  margin-right: 0px;
}
/*=================  高防IP 亿速特点  ==============================*/
.database-specialty .content .database-item .item.gfip-one .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat 0px -190px;
}
.database-specialty .content .database-item .item.gfip-one:hover .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat 0px -275px;
}
.database-specialty .content .database-item .item.gfip-two .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -85px -190px;
}
.database-specialty .content .database-item .item.gfip-two:hover .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -85px -275px;
}
.database-specialty .content .database-item .item.gfip-three .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -170px -190px;
}
.database-specialty .content .database-item .item.gfip-three:hover .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -170px -275px;
}
.database-specialty .content .database-item .item.gfip-four .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -255px -190px;
}
.database-specialty .content .database-item .item.gfip-four:hover .bgimg {
  background: url(../images/gfip-sprite.png) no-repeat -255px -275px;
}
/*=================  云备份 亿速特点  ==============================*/
.backups-specialty .specialtybox {
  width: 300px;
}
.specialty-show .backups-one-specialtybox .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -10px -10px;
}
.specialty-show .backups-one-specialtybox:hover .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -10px -100px;
}
.specialty-show .backups-two-specialtybox .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -100px -10px;
}
.specialty-show .backups-two-specialtybox:hover .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -100px -100px;
}
.specialty-show .backups-three-specialtybox .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -190px -10px;
}
.specialty-show .backups-three-specialtybox:hover .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -190px -100px;
}
.specialty-show .backups-four-specialtybox .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -280px -10px;
}
.specialty-show .backups-four-specialtybox:hover .specialtybox-head {
  background: url("../images/backups_sprite_img.png") no-repeat -280px -100px;
}
/*========== common yisu-specialty end =========*/
/*========== common yisu-ability start =========*/
.product-ability {
  width: 100%;
  background-color: #f0f2f5;
  padding: 100px 0px 60px;
}
.ability-product {
  width: 100%;
  background-color: #f6f8f9;
  padding: 100px 0px 60px;
}
.product-content {
  width: 1200px;
  margin: 0 auto;
}
.product-title {
  height: 134px;
  line-height: 35px;
  font-size: 35px;
  color: #2d3037;
  text-align: center;
}
.ability-kinds > a {
  display: block;
  float: left;
  width: 590px;
  height: 230px;
  margin: 0px 20px 20px 0px;
  background-color: #fff;
  transition: all 0.4s;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.ability-kinds > a.mr-0 {
  margin-right: 0px;
}
.ability-kinds > a.mb-0 {
  margin-bottom: 0px;
}
.ability-kinds > a .content-box {
  width: 580px;
  height: 100%;
  padding: 0px 50px 0px 40px;
}
.ability-kinds > a .content-box > div {
  float: left;
}
.ability-kinds > a .content-box .left-picture {
  width: 80px;
  height: 80px;
  margin: 80px 30px 0px 0px;
}
.ability-kinds > a .content-box .right-content {
  width: 380px;
  height: 100%;
  padding: 44px 0px 0px 0px;
}
.ability-kinds > a .content-box .right-content .content-title {
  height: 38px;
  line-height: 21px;
  font-size: 21px;
  color: #2d3037;
}
.ability-kinds > a .content-box .right-content .content-text p {
  line-height: 24px;
  font-size: 14px;
  color: #5e6d81;
}
.ability-kinds > a:hover {
  -webkit-box-shadow: 0px 0px 20px 5px #eaeced;
  -moz-box-shadow: 0px 0px 20px 5px #eaeced;
  box-shadow: 0px 0px 20px 5px #eaeced;
}
.ability-product .ability-kinds > a {
  -webkit-box-shadow: 0px 0px 20px 5px #eaeced;
  -moz-box-shadow: 0px 0px 20px 5px #eaeced;
  box-shadow: 0px 0px 20px 5px #eaeced;
}
/*========================= 高防IP 产品功能 ================================*/
.ability-kinds .ip-onelink .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -10px -190px;
}
.ability-kinds .ip-onelink:hover .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -10px -280px;
}
.ability-kinds .ip-twolink .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -100px -190px;
}
.ability-kinds .ip-twolink:hover .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -100px -280px;
}
.ability-kinds .ip-threelink .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -190px -190px;
}
.ability-kinds .ip-threelink:hover .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -190px -280px;
}
.ability-kinds .ip-fourlink .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -280px -190px;
}
.ability-kinds .ip-fourlink:hover .left-picture {
  background: url("../images/defense_sprite_img.png") no-repeat -280px -280px;
}
/*========================= 机柜托管 产品功能 ================================*/
.ability-kinds .trustee-onelink .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -10px -393px;
}
.ability-kinds .trustee-onelink:hover .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -10px -483px;
}
.ability-kinds .trustee-twolink .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -100px -393px;
}
.ability-kinds .trustee-twolink:hover .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -100px -483px;
}
.ability-kinds .trustee-threelink .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -190px -393px;
}
.ability-kinds .trustee-threelink:hover .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -190px -483px;
}
.ability-kinds .trustee-fourlink .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -280px -393px;
}
.ability-kinds .trustee-fourlink:hover .left-picture {
  background: url("../images/trustee_sprite_img.png") no-repeat -280px -483px;
}
/*========================= 混合云 产品功能 ================================*/
.gather-ability > a {
  height: 210px;
  padding-top: 25px;
}
.gather-ability a .content-title {
  height: 55px;
  line-height: 55px;
  font-size: 19px;
  color: #2d3037;
}
.gather-ability .content-text p {
  line-height: 28px;
  font-size: 16px;
  color: #5e6d81;
}
.gather-ability .left-picture {
  width: 80px;
  height: 80px;
  margin: 40px 30px 0px 0px;
}
.gather-ability .right-content {
  padding-top: 0;
}
.ability-product .ability-kinds .gather-onelink .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat 0px 0px;
}
.ability-product .ability-kinds .gather-onelink:hover .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat 0px -90px;
}
.ability-product .ability-kinds .gather-twolink .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -90px 0px;
}
.ability-product .ability-kinds .gather-twolink:hover .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -90px -90px;
}
.ability-product .ability-kinds .gather-threelink .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -180px 0px;
}
.ability-product .ability-kinds .gather-threelink:hover .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -180px -90px;
}
.ability-product .ability-kinds .gather-fourlink .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -270px 0px;
}
.ability-product .ability-kinds .gather-fourlink:hover .left-picture {
  background: url("../images/gather_sprite_img.png") no-repeat -270px -90px;
}
/*========== common yisu-ability end =========*/
/*========== common yisu-advantages start =========*/
.yisu-advantages {
  width: 100%;
  background-color: #F0F2F5;
  padding-top: 100px;
  padding-bottom: 78px;
}
.advantage-compare {
  width: 1200px;
  margin: 0 auto;
}
.compare-title {
  height: 112px;
  line-height: 36px;
  font-size: 36px;
  color: #2D3037;
  text-align: center;
}
.comparebox {
  float: left;
  width: 280px;
  background-color: #fff;
}
.compare-head {
  height: 100px;
  line-height: 100px;
  font-size: 18px;
  color: #2D3037;
  background-color: #F6F8F9;
  text-align: center;
}
.comparebox.yisu-comparebox .compare-head {
  position: relative;
  height: 120px;
  line-height: 120px;
  font-size: 21px;
  color: #FF6666;
  background-color: #fff;
  border-bottom: 1px solid #f6f9f8;
}
.yisu-comparebox .compare-head i {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 40px;
  background: url("../images/cloud_sprite_img.png") no-repeat -550px -102px;
}
.comparebox ul li {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  text-align: center;
  color: #5E6D81;
  border-bottom: 1px solid #F6F8F9;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.comparebox.yisu-comparebox ul li {
  color: #2d3137;
}
.comparebox.yisu-comparebox {
  position: relative;
  width: 360px;
  -webkit-box-shadow: 0px 0px 20px 5px #F4F4F4;
  -moz-box-shadow: 0px 0px 20px 5px #F4F4F4;
  box-shadow: 0px 0px 20px 5px #F4F4F4;
  margin-top: -20px;
}
.compareboxyisu-comparebox ul li {
  color: #2D3037;
}
.comparebox.yisu-comparebox ul li.last-compare-item {
  padding-bottom: 20px;
}
.comparebox ul li.last-compare-item {
  border-bottom: none;
}
/*========================== 物理服务器 优势 ============================*/
.compare-show .yisu-physics {
  width: 440px;
}
.compare-show .other-physics {
  width: 380px;
}
.yisu-physics ul {
  padding-left: 122px;
}
.yisu-comparebox.yisu-physics ul li {
  position: relative;
  text-align: left;
  padding-left: 40px;
}
.yisu-physics ul li::before {
  position: absolute;
  left: 0px;
  top: 20px;
  content: '';
  width: 10px;
  height: 10px;
  background-color: #FF6666;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
/*========================== 专用宿主机 优势 ============================*/
.mainframe-compare .comparebox {
  width: 200px;
}
.mainframe-compare .w390 {
  width: 390px;
}
.mainframe-compare .comparebox.yisu-comparebox {
  width: 610px;
}
/* luojinshu advantage 2019-07-06 start */
.compare-show.luojinshu .compare_item {
  width: 300px;
}
.compare-show.luojinshu .yisu-comparebox {
  width: 480px;
}
.compare-show.luojinshu .traditional {
  width: 360px;
}
.compare-show.luojinshu .compare_menu {
  float: left;
  width: 60px;
  height: 100%;
  background-color: #DBE1E8;
}
.compare-show.luojinshu .compare_menu .head {
  height: 100px;
  border-bottom: 1px solid #F0F2F5;
}
.compare-show.luojinshu .compare_menu ul li {
  font-size: 16px;
  color: #2D3037;
  text-align: center;
  border-bottom: 1px solid #F0F2F5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.compare-show.luojinshu .compare_menu ul li.one {
  height: 200px;
  padding-top: 50px;
}
.compare-show.luojinshu .compare_menu ul li.two {
  height: 300px;
  padding-top: 87px;
}
.compare-show.luojinshu .compare_menu ul li.three {
  height: 100px;
  border-bottom: none;
}
/* luojinshu advantage 2019-07-06 end */
/* hongkong-high-defense page advantage 2019-07-30 start */
.yisu-advantages .advantage-compare .hk-high-title {
  height: 86px;
}
.yisu-advantages .advantage-compare .advantages_tab {
  text-align: center;
  padding-bottom: 96px;
}
.yisu-advantages .advantage-compare .advantages_tab ul {
  display: inline-block;
  height: 40px;
  vertical-align: top;
}
.yisu-advantages .advantage-compare .advantages_tab ul li {
  position: relative;
  float: left;
  width: 140px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  font-size: 16px;
  color: #5E6D81;
  border: 1px solid #DBE1E8;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}
.yisu-advantages .advantage-compare .advantages_tab ul li.last {
  margin-left: -1px;
  -webkit-border-radius: 0px 4px 4px 0px;
  -moz-border-radius: 0px 4px 4px 0px;
  -o-border-radius: 0px 4px 4px 0px;
  border-radius: 0px 4px 4px 0px;
}
.yisu-advantages .advantage-compare .advantages_tab ul li:nth-child(1) {
  -webkit-border-radius: 4px 0px 0px 4px;
  -moz-border-radius: 4px 0px 0px 4px;
  -o-border-radius: 4px 0px 0px 4px;
  border-radius: 4px 0px 0px 4px;
}
.yisu-advantages .advantage-compare .advantages_tab ul li.active {
  color: #FFFFFF;
  background-color: #ff6666;
  border-color: #FF6666;
  z-index: 2;
}
.yisu-advantages .advantage-compare .compare-show-box .compare-show {
  display: none;
}
.yisu-advantages .advantage-compare .compare-show-box .compare-show.show {
  display: block;
}
/* hongkong-high-defense page advantage 2019-07-30 end */
/* 2019-11-14 cloud-server advandages change start */
.comparebox ul li {
  height: 50px;
  line-height: 50px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.comparebox ul li.two-lines {
  height: 74px !important;
}
.comparebox ul li.two-lines p {
  line-height: 30px;
}
.compare_menu {
  float: left;
  width: 60px;
  height: 100%;
  background-color: #DBE1E8;
}
.compare_menu .head {
  height: 100px;
  font-size: 18px;
  color: #2D3037;
  text-align: center;
  padding-top: 25px;
  border-bottom: 1px solid #F0F2F5;
}
.compare_menu ul li {
  line-height: 25px;
  font-size: 16px;
  color: #2D3037;
  text-align: center;
  border-bottom: 1px solid #F0F2F5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
#hk_huodong_cloud .compare_menu ul li.one {
  height: 150px;
  padding-top: 25px;
}
#hk_huodong_cloud .compare_menu ul li.two,
#hk_huodong_cloud .compare_menu ul li.four {
  height: 150px;
  padding-top: 50px;
}
#hk_huodong_cloud .compare_menu ul li.three {
  height: 100px;
  padding-top: 25px;
}
#hk_huodong_cloud .comparebox.ability {
  width: 300px;
}
#hk_huodong_cloud .comparebox.yisu-comparebox {
  width: 840px;
}
#hk_huodong_cloud .comparebox ul li.last-compare-item {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
#hk_huodong_luojinshu .compare_menu ul li.one {
  height: 200px;
  padding-top: 50px;
}
#hk_huodong_luojinshu .compare_menu ul li.two {
  height: 300px;
  padding-top: 87px;
}
#hk_huodong_luojinshu .compare_menu ul li.three {
  height: 100px;
}
#hk_huodong_luojinshu .comparebox.compare_item {
  width: 300px;
}
#hk_huodong_luojinshu .comparebox.yisu-comparebox {
  width: 480px;
}
#hk_huodong_luojinshu .comparebox.traditional {
  width: 360px;
}
#hk_huodong_luojinshu .comparebox ul li.last-compare-item {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
/* 2019-11-14 cloud-server advandages change start */
/*========== common yisu-advantages end =========*/
/* 亿速云防御架构 */
.defense_structure {
  width: 100%;
  height: 970px;
  background: #13182C;
  padding-top: 85px;
}
.defense_structure .content {
  color: #FFFFFF;
  text-align: center;
}
.defense_structure .content h2 {
  line-height: 46px;
  font-size: 36px;
}
.defense_structure .content p {
  line-height: 28px;
  font-size: 18px;
  margin-top: 30px;
  opacity: 0.6;
}
.defense_structure .structure-tab {
  text-align: center;
  padding-top: 35px;
}
.defense_structure .structure-tab > ul {
  display: inline-block;
  height: 40px;
  vertical-align: top;
}
.defense_structure .structure-tab > ul > li {
  position: relative;
  float: left;
  height: 40px;
  line-height: 38px;
  font-size: 16px;
  color: #FFF;
  padding: 0px 30px;
  border: 1px solid #DBE1E8;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  opacity: 0.5;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  cursor: pointer;
}
.defense_structure .structure-tab > ul > li.last {
  margin-left: -1px;
}
.defense_structure .structure-tab > ul > li.active {
  background-color: #FF6666;
  border-color: #FF6666;
  opacity: 1;
  z-index: 2;
}
.defense_structure .structure-item {
  text-align: center;
}
.defense_structure .structure-item ul li {
  display: none;
}
.defense_structure .structure-item ul li img {
  display: inline-block;
  max-width: 100%;
  vertical-align: top;
}
.defense_structure .structure-item ul li.jiagou-one {
  padding-top: 36px;
}
.defense_structure .structure-item ul li.jiagou-two {
  padding-top: 65px;
}
.defense_structure .structure-item ul li.show {
  display: block;
}
/*==================== 选择镜像弹窗 start ================*/
.system_modal {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}
.system_modal .center {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -230px;
  margin-top: -189px;
  width: 460px;
  background-color: #fff;
}
.system_modal .center .head {
  position: relative;
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  color: #5E6D81;
  background-color: #F6F8F9;
  padding-left: 20px;
}
.system_modal .center .head span {
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 22px;
  cursor: pointer;
}
.system_modal .center .body {
  background-color: #fff;
  padding: 30px 40px 40px;
}
.system_modal .center .body .cmMod {
  position: relative;
  margin-top: 20px;
}
.system_modal .center .body .cmMod .cmClear {
  position: relative;
  left: 0px;
  right: 0px;
  width: 100%;
}
.system_modal .center .body .tsZdx {
  position: relative;
}
.system_modal .center .body .cmRit {
  width: 100%;
  float: left;
}
.system_modal .center .body .cmInRt {
  width: 100%;
  position: relative;
}
.system_modal .center .body .monitor-select {
  height: 34px;
  width: 100%;
  border: 1px solid #DBE1E8;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  float: left;
  text-align: left;
  position: relative;
  left: 0;
  top: 0;
  zoom: 1;
  box-sizing: content-box;
  margin-bottom: 20px;
}
.system_modal .center .body .monitor-select .values {
  height: 22px;
  border: none;
  background: none;
  text-align: center;
  font: 14px/23px microsoft yahei;
  width: 136px;
  float: left;
  margin-left: 12px;
  _margin-left: 6px;
  margin-top: 6px;
  width: 180px;
  text-align: left;
  color: #000;
  outline: none;
}
.system_modal .center .body .monitor-select .rit-ars {
  width: 30px;
  float: right;
  overflow: hidden;
  height: 34px;
  background: url(../images/dpdIcs.png) no-repeat center;
  position: absolute;
  right: 0;
  top: 0;
}
.system_modal .center .body .mntv2 {
  width: 100%;
  margin-left: -1px;
  border-radius: 0;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  margin-bottom: 0px;
}
.system_modal .center .body .erInfo {
  background: #f9ebeb;
  border-color: #e95048;
}
.system_modal .center .body .secTps {
  display: block;
  text-align: left;
  font-size: 12px;
  color: #9d9d9d;
  padding: 10px 0 0 0;
}
.system_modal .center .body .monitor-select .chi-con,
.system_modal .center .body .monitor-selects .chi-con {
  height: auto;
  position: absolute;
  left: -1px;
  top: 34px;
  border: 1px solid #DBE1E8;
  width: 100%;
  display: none;
  overflow: hidden;
  background: #fff;
  box-sizing: content-box;
  z-index: 10;
}
.system_modal .center .body .monitor-select .chi-con .in-con,
.system_modal .center .body .monitor-selects .chi-con .in-con {
  width: 100%;
  overflow: hidden;
}
.system_modal .center .body .monitor-select .chi-con .opts,
.system_modal .center .body .monitor-selects .chi-con .opts {
  position: relative;
  display: block;
  height: 32px;
  font: 14px/32px microsoft yahei;
  text-align: left;
  padding-left: 36px;
  overflow: hidden;
  outline: none;
  cursor: default;
  color: #585858;
  transition: background linear 0.2s;
  cursor: pointer;
}
.system_modal .center .body .monitor-select .chi-con .opts i {
  position: absolute;
  left: 8px;
  top: 7px;
  width: 18px;
  height: 18px;
}
.system_modal .center .body .windows-i {
  background: url("../images/serverdiy_sprite_img.png") no-repeat -169px -10px;
}
.system_modal .center .body .centos-i {
  background: url("../images/serverdiy_sprite_img.png") no-repeat -196px -10px;
}
.system_modal .center .body .ubuntu-i {
  background: url("../images/serverdiy_sprite_img.png") no-repeat -224px -10px;
}
.system_modal .center .body .debian-i {
  background: url("../images/serverdiy_sprite_img.png") no-repeat -252px -10px;
}
.system_modal .center .body .monitor-select .chi-con .opts:hover,
.system_modal .center .body .monitor-selects .chi-con .opts:hover {
  background-color: #f2efef;
  text-decoration: none;
}
.system_modal .center .body .mntv2 .values {
  width: 292px;
}
.system_modal .center .body .secTps {
  display: block;
  text-align: left;
  font-size: 12px;
  color: #9d9d9d;
  padding: 10px 0 0 0;
}
.system_modal .center .body .sys-select > div {
  z-index: 5;
  display: none;
}
.system_modal .center .body > p {
  line-height: 24px;
  font-size: 14px;
  color: #9EA7B3;
  padding: 15px 0px 55px;
}
.system_modal .center .body > button {
  width: 100%;
  height: 46px;
  font-size: 16px;
  color: #FFFFFF;
  background-color: #FF6666;
  border: 1px solid #FF6666;
  outline: none;
  cursor: pointer;
}
.system_modal .center .body > button:hover {
  background-color: #FF8888;
  border-color: #FF8888;
}
.system_modal .center .body > button:active {
  background-color: #FF4444;
  border-color: #FF4444;
}
/*==================== 选择镜像弹窗 end ================*/
/*======= 公共 专用宿主机 start =======*/
.banner.mainframe-banner {
  background-color: #0A4C9F;
}
.banner.trusteeship-banner {
  background-color: #4042B7;
}
.banner.gather-banner,
.banner.yisuip-banner {
  background-color: #4E24B2;
}
.banner.japan-banner {
  background-color: #1473A1;
}
.banner.germany-banner,
.banner.usa-banner {
  background-color: #5D1AAF;
}
.yisu-comparebox .last-compare-item {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box !important;
}
.info-bot .subuser-hide {
  color: #FF6666;
}
.info-bot .subuser-hide:hover {
  color: red;
}
.gather-ability .left-picture,
.ability-kinds .left-picture {
  margin: 40px 30px 0px 0px !important;
}
#yisuip-ability-kinds > a,
#database-ability-kinds > a {
  display: block;
  float: left;
  width: 280px;
  height: 310px;
  background-color: #fff;
  transition: all 0.4s;
  padding-top: 49px;
  margin: 0px 26px 0px 0px;
  -webkit-box-shadow: 0px 0px 10px 8px #eaeced;
  -moz-box-shadow: 0px 0px 10px 8px #eaeced;
  -o-box-shadow: 0px 0px 10px 8px #eaeced;
  box-shadow: 0px 0px 10px 8px #eaeced;
}
#yisuip-ability-kinds > a.mr-0,
#database-ability-kinds > a.mr-0 {
  margin-right: 0px;
}
#yisuip-ability-kinds > a .content-box,
#database-ability-kinds > a .content-box {
  width: 100%;
  height: 100%;
  padding: 0px;
}
#yisuip-ability-kinds .left-picture,
#database-ability-kinds .left-picture {
  float: inherit;
  width: 72px;
  height: 78px;
  margin: 0 auto !important;
}
#yisuip-ability-kinds .right-content,
#database-ability-kinds .right-content {
  width: 100%;
  height: auto;
  padding: 0px;
  margin-top: 34px;
  text-align: center;
}
#yisuip-ability-kinds .right-content .content-title,
#database-ability-kinds .right-content .content-title {
  height: 36px;
  line-height: 20px;
  font-size: 20px;
  color: #2d3037;
}
#yisuip-ability-kinds .right-content .content-text p,
#database-ability-kinds .right-content .content-text p {
  line-height: 28px;
  font-size: 16px;
  color: #5e6d81;
}
#database-ability-kinds > div {
  height: 340px;
}
#database-ability-kinds .left-picture {
  width: 80px;
  height: 80px;
}
.yisuserver-nav ul li h2 {
  margin: 0px;
}
/*======= 公共 专用宿主机 end =======*/
/*================= 2020-01-13 start ========================*/
.two-specialtybox .specialtybox-head {
  background: url(../images/slb-msg.png) no-repeat -85px 0px;
}
.two-specialtybox:hover .specialtybox-head {
  background: url(../images/slb-msg.png) no-repeat -85px -85px;
}
.five-specialtybox .specialtybox-head {
  background: url(../images/cloud_sprite_img.png) no-repeat -370px -10px;
}
/*.five-specialtybox:hover .specialtybox-head {*/
  /*background: url(../images/cdn-sprite.png) no-repeat -340px -275px;*/
/*}*/
.database-specialty .content .database-item .item.japan-one .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -10px -10px;
}
.database-specialty .content .database-item .item.japan-one:hover .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -10px -100px;
}
.database-specialty .content .database-item .item.japan-two .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px 0px;
}
.database-specialty .content .database-item .item.japan-two:hover .bgimg {
  background: url(../images/slb-msg.png) no-repeat -85px -85px;
}
.database-specialty .content .database-item .item.japan-three .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -190px -10px;
}
.database-specialty .content .database-item .item.japan-three:hover .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -190px -100px;
}
.database-specialty .content .database-item .item.japan-four .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -280px -10px;
}
.database-specialty .content .database-item .item.japan-four:hover .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -280px -100px;
}
.database-specialty .content .database-item .item.japan-five .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -370px -10px;
}
.database-specialty .content .database-item .item.japan-five:hover .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -370px -100px;
}
.database-specialty .content .database-item .item.japan-six .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -460px -10px;
}
.database-specialty .content .database-item .item.japan-six:hover .bgimg {
  background: url(../images/cloud_sprite_img.png) no-repeat -460px -100px;
}
/*================= 2020-01-13 end ========================*/
/*================= 2020-01-14 start ========================*/
.database-specialty .content .database-item .item {
  width: 540px;
  margin-right: 120px;
}
.database-specialty .content .database-item .item.mr-0 {
  margin-right: 0px;
}
.database-specialty .content .database-item#jigui-database-item .item {
  height: 160px;
}
.database-specialty .content .database-item .item.jigui-one .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px 0px;
}
.database-specialty .content .database-item .item.jigui-one:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -85px;
}
.database-specialty .content .database-item .item.jigui-two .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px 0px;
}
.database-specialty .content .database-item .item.jigui-two:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -85px;
}
.database-specialty .content .database-item .item.jigui-three .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px 0px;
}
.database-specialty .content .database-item .item.jigui-three:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -85px;
}
.database-specialty .content .database-item .item.jigui-four .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px 0px;
}
.database-specialty .content .database-item .item.jigui-four:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -85px;
}
.database-specialty .content .database-item .item.ip-one .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -360px;
}
.database-specialty .content .database-item .item.ip-one:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -445px;
}
.database-specialty .content .database-item .item.ip-two .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -360px;
}
.database-specialty .content .database-item .item.ip-two:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -445px;
}
.database-specialty .content .database-item .item.ip-three .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -360px;
}
.database-specialty .content .database-item .item.ip-three:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -445px;
}
.database-specialty .content .database-item .item.ip-four .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -360px;
}
.database-specialty .content .database-item .item.ip-four:hover .bgimg {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -445px;
}
.ability-kinds .trustee-onelink .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -170px;
}
.ability-kinds .trustee-onelink:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -255px;
}
.ability-kinds .trustee-twolink .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -170px;
}
.ability-kinds .trustee-twolink:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -255px;
}
.ability-kinds .trustee-threelink .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -170px;
}
.ability-kinds .trustee-threelink:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -255px;
}
.ability-kinds .trustee-fourlink .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -170px;
}
.ability-kinds .trustee-fourlink:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -255px;
}
.ability-kinds .ip-one .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -530px;
}
.ability-kinds .ip-one:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat 0px -615px;
}
.ability-kinds .ip-two .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -530px;
}
.ability-kinds .ip-two:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -85px -615px;
}
.ability-kinds .ip-three .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -530px;
}
.ability-kinds .ip-three:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -170px -615px;
}
.ability-kinds .ip-four .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -530px;
}
.ability-kinds .ip-four:hover .left-picture {
  background: url(../images/jigui-ip-sprite.png) no-repeat -255px -615px;
}
/*================= 2020-01-14 end ========================*/
[v-cloak] {
  display: none;
}
.hotlink .hotlink-box > ul > li a.selected {
  background: #39424F;
}
.hotlink .hotlink-box > ul > li a.selected span {
  color: #FF6666;
  opacity: 1;
}
.helpbox .help-link p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.note {display: -ms-flexbox;display: flex;padding: 10px;color: #73777a;background-color: #ffe0e0;margin-bottom: 5px;}
.note-content a {
  color:#73777a;
}
.instance_sysimg_tip{
  color: #2D3037;
  background-color: #FFE0E0;
  line-height: 20px;
  padding: 10px 24px;
  margin: 16px 0px 0px;
}
.system_modal .center .body{
  padding-top:0px;
}

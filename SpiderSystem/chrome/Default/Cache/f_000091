@charset "utf-8";
body a {
  -webkit-transition: all 0s !important;
  -moz-transition: all 0s !important;
  -o-transition: all 0s !important;
  transition: all 0s !important;
}
/* =========== zixun index start =============*/
.zixun-banner {
  width: 100%;
  height: 340px;
  background: url(../../images/information/zixun-banner.jpg) no-repeat center;
  padding-top: 145px;
}
.zixun-banner.list-zixun-banner {
  background: url(../../images/information/list-guanggao.jpg) no-repeat center;
}
.zixun-banner .zixun-banner-content {
  width: 1200px;
  margin: 0 auto;
}
.zixun-banner .zixun-banner-content h1 {
  line-height: 70px;
  font-size: 40px;
  color: #FFFFFF;
}
.zixun-banner .zixun-banner-content p {
  line-height: 28px;
  font-size: 16px;
  color: #FFFFFF;
  padding-top: 10px;
  opacity: 0.5;
}
.content {
  width: 1200px;
  margin: 0 auto;
}
.zixun-title {
  height: 80px;
  line-height: 80px;
  border-bottom: 1px solid #DBE1E8;
}
.zixun-title > span,
.zixun-title > h2 {
  display: inline-block;
  height: 80px;
  font-size: 24px;
  font-weight: 400;
  color: #2D3037;
  border-bottom: 3px solid #FF6666;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  vertical-align: top;
}
.zixun-title a {
  float: right;
  font-size: 14px;
  color: #9EA7B3;
}
.zixun-title a:hover {
  color: #FF6666;
}
.zixun-hot-dot {
  width: 100%;
  min-height: 560px;
  background-color: #fff;
  padding: 32px 0px 66px;
}
.zixun-hot-dot .zixun-hot-dot-box {
  position: relative;
  padding-right: 320px;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list {
  width: 100%;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item {
  float: left;
  width: 390px;
  padding: 26px 0px 12px;
  margin-right: 50px;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item h3 {
  line-height: 48px;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item h3 a {
  color: #2D3037;
  -webkit-transition: all 0s;
  -moz-transition: all 0s;
  -o-transition: all 0s;
  transition: all 0s;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item h3 a:hover {
  color: #FF6666;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item p {
  line-height: 26px;
  font-size: 14px;
  color: #5E6D81;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  cursor:pointer;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-head .hot-dot-list-item .hot-dot-time {
  line-height: 40px;
  font-size: 14px;
  color: #9EA7B3;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-bottom > ul > li {
  position: relative;
  float: left;
  width: 390px;
  height: 50px;
  line-height: 50px;
  padding-left: 15px;
  border-top: 1px solid #DBE1E8;
  margin-right: 50px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-bottom > ul > li::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 22px;
  width: 6px;
  height: 6px;
  background-color: #FF6666;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-bottom > ul > li h3 {
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-bottom > ul > li h3 a {
  font-size: 16px;
  color: #2D3037;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-list .hot-dot-list-bottom > ul > li h3 a:hover {
  color: #FF6666;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-img {
  position: absolute;
  right: 0px;
  top: 40px;
  width: 320px;
  height: 340px;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-img a {
  display: block;
  width: 100%;
  height: 100%;
}
.zixun-hot-dot .zixun-hot-dot-box .hot-dot-img a img {
  display: block;
  width: 100%;
  height: 100%;
}
.zixun-server {
  width: 100%;
  min-height: 650px;
  background-color: #F0F2F5;
  padding: 32px 0px 66px;
}
.zixun-server .zixun-server-box {
  position: relative;
  padding: 40px 0px 0px 340px;
}
.zixun-server .zixun-server-box .zixun-server-img {
  position: absolute;
  left: 0px;
  top: 40px;
  width: 320px;
  height: 430px;
}
.zixun-server .zixun-server-box .zixun-server-img a {
  display: block;
  width: 100%;
  height: 100%;
}
.zixun-server .zixun-server-box .zixun-server-img img {
  display: block;
  width: 100%;
  height: 100%;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li {
  float: left;
  width: 410px;
  height: 130px;
  background-color: #fff;
  padding: 14px 30px 0px 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0px 0px 20px 20px;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li:hover {
  -webkit-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li.mb-0 {
  margin-bottom: 0px;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li h3 {
  position: relative;
  width: 100%;
  line-height: 42px;
  font-size: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 20px;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li h3::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -12px;
  width: 3px;
  height: 24px;
  background-color: #FF6666;
  z-index: 10;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li h3 a {
  color: #2D3037;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li h3 a:hover {
  color: #FF6666;
}
.zixun-server .zixun-server-box .zixun-server-list > ul > li p {
  width: 100%;
  line-height: 26px;
  font-size: 14px;
  color: #9EA7B3;
  padding-left: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  cursor:pointer;
}
.zixun-database {
  width: 100%;
  background-color: #FFF;
  padding: 32px 0px 66px;
}
.zixun-database .zixun-database-box {
  padding-top: 10px;
}
.zixun-database .zixun-database-box .zixun-database-list {
  float: left;
  width: 880px;
  padding-right: 50px;
}
.zixun-database .zixun-database-box .zixun-database-list > ul > li {
  padding: 16px 0px 23px;
  border-bottom: 1px solid #DBE1E8;
}
.zixun-database .zixun-database-box .zixun-database-list > ul > li h3 {
  line-height: 48px;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.zixun-database .zixun-database-box .zixun-database-list > ul > li h3 a {
  color: #2D3037;
}
.zixun-database .zixun-database-box .zixun-database-list > ul > li h3 a:hover {
  color: #FF6666;
}
.zixun-database .zixun-database-box .zixun-database-list > ul > li p {
  width: 100%;
  line-height: 26px;
  font-size: 14px;
  color: #9EA7B3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  cursor:pointer;
}
.zixun-database .zixun-database-box .zixun-database-rank {
  float: right;
  width: 320px;
  padding-top: 10px;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-title {
  height: 60px;
  border-bottom: 1px solid #DBE1E8;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-title span {
  display: inline-block;
  height: 100%;
  line-height: 60px;
  font-size: 20px;
  color: #2D3037;
  border-bottom: 1px solid #FF6666;
  vertical-align: top;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-list {
  padding-top: 14px;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-list > ul {
  list-style-type: decimal;
  list-style-position: inside;
  color: #5E6D81;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-list > ul > li {
  line-height: 42px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-list > ul > li a {
  color: #5E6D81;
  vertical-align: top;
}
.zixun-database .zixun-database-box .zixun-database-rank .zixun-database-rank-list > ul > li a:hover {
  color: #FF6666;
}
.zixun-product {
  width: 100%;
  background-color: #FFF;
  padding: 0px 0px 80px;
}
.zixun-product .content .zixun-product-item {
  float: left;
  width: 380px;
  margin-right: 30px;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
}
.zixun-product .content .zixun-product-item.mr-0 {
  margin-right: 0px;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head {
  position: relative;
  width: 100%;
  height: 110px;
  padding: 29px 29px 0px;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head.zixun-head-code {
  background: url(../../images/information/zixun-head-code.jpg) no-repeat center;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head.zixun-head-safe {
  background: url(../../images/information/zixun-head-safe.jpg) no-repeat center;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head.zixun-head-domain {
  background: url(../../images/information/zixun-head-domain.jpg) no-repeat center;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head h2 {
  line-height: 34px;
  font-size: 24px;
  color: #FFFFFF;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head .zixun-product-item-head-line {
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: #FF6666;
  margin-top: 24px;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head a {
  position: absolute;
  right: 20px;
  bottom: 14px;
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.5;
}
.zixun-product .content .zixun-product-item .zixun-product-item-head a:hover {
  color: #FF6666;
  opacity: 1;
}
.zixun-product .content .zixun-product-item .zixun-product-item-list {
  background-color: #fff;
  padding: 14px 30px 20px;
}
.zixun-product .content .zixun-product-item .zixun-product-item-list > ul > li {
  position: relative;
  line-height: 44px;
  font-size: 16px;
  padding-left: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.zixun-product .content .zixun-product-item .zixun-product-item-list > ul > li::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -3px;
  width: 6px;
  height: 6px;
  background-color: #FF6666;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
.zixun-product .content .zixun-product-item .zixun-product-item-list > ul > li a {
  color: #2D3037;
}
.zixun-product .content .zixun-product-item .zixun-product-item-list > ul > li a:hover {
  color: #FF6666;
}
/* =========== zixun index end =============*/
/* =========== system maintenance start =============*/
.information {
  width: 100%;
  background-color: #fff;
  padding: 40px 0px 80px;
}
.information .content .information-left-box {
  float: left;
  width: 880px;
  padding-right: 50px;
}
.information .content .information-left-box .information-left-breadcrumb {
  border-bottom: 1px solid #DBE1E8;
}
.information .content .information-left-box .information-left-breadcrumb > ul > li {
  line-height: 54px;
  font-size: 14px;
  float: left;
  color: #9EA7B3;
}
.information .content .information-left-box .information-left-breadcrumb > ul > li a {
  color: #FF6666;
}
.information .content .information-left-box .information-left-breadcrumb > ul > li a:hover {
  text-decoration: underline;
}
.information .content .information-left-box .information-left-list > ul > li {
  padding: 16px 0px 17px;
  border-bottom: 1px solid #DBE1E8;
}
.information .content .information-left-box .information-left-list > ul > li h2 {
  line-height: 48px;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.information .content .information-left-box .information-left-list > ul > li h2 a {
  color: #2D3037;
}
.information .content .information-left-box .information-left-list > ul > li p {
  line-height: 26px;
  font-size: 14px;
  color: #9EA7B3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  cursor: pointer;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag {
  margin-top: 15px;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .tag-list {
  display: inline-block;
  vertical-align: top;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .tag-list a {
  float: left;
  height: 18px;
  line-height: 16px;
  font-size: 12px;
  color: #5E6D81;
  padding: 0px 7px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  margin: 0px 10px 10px 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .tag-list a:hover {
  color: #FF6666;
  border-color: #FF6666;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .information-list-author {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 14px;
  color: #9EA7B3;
  vertical-align: top;
  margin-right: 10px;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .information-list-author a {
  color: #9EA7B3;
}
.information .content .information-left-box .information-left-list > ul > li .information-list-tag .information-list-time {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 14px;
  color: #9EA7B3;
  vertical-align: top;
}
.information .content .information-left-box .information-left-list > ul > li:hover {
  background: -webkit-linear-gradient(left, #FFF, #F0F2F5, #FFF);
  background: -o-linear-gradient(right, #FFF, #F0F2F5, #FFF);
  background: -moz-linear-gradient(right, #FFF, #F0F2F5, #FFF);
  background: linear-gradient(right, #FFF, #F0F2F5, #FFF);
}
.information .content .information-left-box .information-left-list > ul > li:hover h2 a {
  color: #FF6666;
}
.information .content .information-left-box .information-left-list > ul > li:hover h2 a:hover {
  text-decoration: underline;
}
.information .content .information-right-box {
  float: right;
  width: 320px;
}
.information .content .information-right-box .information-right-img {
  padding-top: 20px;
  width: 100%;
  /*height: 180px;*/
}
.information .content .information-right-box .information-right-img a {
  display: block;
  width: 100%;
  height: 100%;
}
.information .content .information-right-box .information-right-img img {
  display: block;
  width: 100%;
  height: 100%;
}
.information .content .information-right-box {
  padding-top: 30px;
}
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-title {
  height: 60px;
  line-height: 60px;
  border-bottom: 1px solid #DBE1E8;
}
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-title h2,
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-title h3 {
  display: inline-block;
  height: 100%;
  font-size: 20px;
  color: #2D3037;
  border-bottom: 1px solid #FF6666;
  vertical-align: top;
}
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-list {
  padding: 20px 0px 20px;
}
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-list a {
  float: left;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
  color: #5E6D81;
  padding: 0px 12px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  margin: 0px 10px 10px 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.information .content .information-right-box .information-right-relevant-tag .relevant-tag-list a:hover {
  color: #FF6666;
  border-color: #FF6666;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul {
  height: 60px;
  border-bottom: 1px solid #DBE1E8;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li {
  float: left;
  width: 50%;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-bottom: 1px solid transparent;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li h2,
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li h3 {
  font-size: 20px;
  color: #2D3037;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li.active {
  border-bottom-color: #FF6666;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li.active h2,
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li.active h3 {
  color: #FF6666;
}
.information .content .information-right-box .information-right-recommend .recommend-tab > ul > li.default-status {
  display: inline-block;
  width: auto;
  color: #2D3037;
  border-bottom: 1px solid #FF6666;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item {
  display: none;
  padding-top: 14px;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item.show {
  display: block;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item > ul {
  list-style-type: decimal;
  list-style-position: inside;
  color: #5E6D81;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item > ul > li {
  line-height: 42px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item > ul > li a {
  color: #5E6D81;
}
.information .content .information-right-box .information-right-recommend .recommend-tab-list .recommend-tab-list-item > ul > li a:hover {
  color: #FF6666;
}
.information-main-tag {
  width: 100%;
  background-color: #fff;
  padding-top: 60px;
}
.information-main-tag .content .information-main-tag-title {
  height: 36px;
  line-height: 36px;
}
.information-main-tag .content .information-main-tag-title span {
  font-size: 36px;
  color: #FF6666;
}
.information-main-tag .content .information-main-tag-title h1 {
  display: inline-block;
  height: 36px;
  line-height: 34px;
  font-size: 36px;
  color: #2D3037;
  vertical-align: top;
  margin-left: 12px;
}
.information-top-breadcrumb-box {
  width: 100%;
  background-color: #fff;
  padding-top: 20px;
}
.information-top-breadcrumb-box .content .information-top-breadcrumb {
  border-bottom: 1px solid #DBE1E8;
}
.information-top-breadcrumb-box .content .information-top-breadcrumb > ul > li {
  line-height: 54px;
  font-size: 14px;
  float: left;
  color: #9EA7B3;
}
.information-top-breadcrumb-box .content .information-top-breadcrumb > ul > li a {
  color: #FF6666;
}
.information-top-breadcrumb-box .content .information-top-breadcrumb > ul > li a:hover {
  text-decoration: underline;
}
.information-details {
  padding-top: 17px;
}
.information-details h1 {
  line-height: 36px;
  font-size: 30px;
  color: #2D3037;
  padding: 0px 0px 20px;
}
.information-details .information-details-msg {
  line-height: 24px;
  font-size: 14px;
  color: #9EA7B3;
  margin-top: 20px;
}
.information-details .information-details-msg > span {
  margin-right: 16px;
}
.information-details p {
  line-height: 30px;
  font-size: 16px;
  color: #2D3037;
  margin: 24px 0px;
  word-break: break-all;
}
.information-details {
  color: #2D3037;
}
.information-details h2,
.information-details h3,
.information-details h4,
.information-details h5,
.information-details h6 {
  color: #2D3037;
}
.information-details a {
  color: #FF6666;
}
.information-details a:hover {
  text-decoration: underline !important;
}
.information-details img {
  max-width: 100%;
  border: 0;
  vertical-align: middle;
}
.information-details h2 {
  position: relative;
  line-height: 22px;
  font-size: 23px;
  margin-top: 16px;
  margin-bottom: 16px;
  line-height: 1.4;
  padding-bottom: 0;
}
.information-details h3 {
  position: relative;
  line-height: 20px;
  font-size: 20px;
  margin-top: 30px;
  margin-bottom: 12px;
  line-height: 1.4;
  padding-bottom: 0;
}
.information-details h4 {
  position: relative;
  line-height: 20px;
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 10px;
  line-height: 1.4;
  padding-bottom: 0;
}
.information-details h5 {
  position: relative;
  line-height: 20px;
  font-size: 16px;
  margin-top: 16px;
  margin-bottom: 10px;
  line-height: 1.4;
  padding-bottom: 0;
}
.information-details h6 {
  position: relative;
  line-height: 20px;
  font-size: 15px;
  margin-top: 12px;
  margin-bottom: 10px;
  line-height: 1.4;
  padding-bottom: 0;
}
.information-details pre {
  font-size: 14px;
  color: #2D3037;
  background-color: #F0F2F5;
  padding: 15px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  margin: 8px 0px;
}
.information-details blockquote {
  position: relative;
  line-height: 24px;
  font-size: 14px;
  color: #2D3037;
  background: #F0F2F5;
  padding: 10px 10px 10px 20px;
  border-left: 4px solid #ff6666;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  margin: 20px 0;
  word-wrap: break-word;
  word-break: break-all;
}
.information-details blockquote strong,
.information-details blockquote em {
  font-size: 14px;
  color: #2D3037;
}
.information-details ul {
  list-style: disc;
  list-style-position: inside;
}
.information-details ul li {
  line-height: 26px;
  font-size: 14px;
  color: #2D3037;
  margin: 6px 0px;
  word-break: break-all;
}
.information-details ol {
  list-style: decimal;
  list-style-position: inside;
}
.information-details ol li {
  line-height: 26px;
  font-size: 14px;
  color: #2D3037;
  margin: 6px 0px;
  word-break: break-all;
}
.information-details table {
  width: 100%;
  font-size: 14px;
  margin: 12px 0px;
  border-collapse: collapse;
  border: 1px solid #DBE1E8;
}
.information-details table tr th {
  text-align: left;
  padding: 8px 13px;
  border-right: 1px solid #DBE1E8;
  border-bottom: 1px solid #DBE1E8;
  background-color: #f0f2f5;
  word-break: break-all;
}
.information-details table tbody tr td {
  padding: 8px 13px;
  border-right: 1px solid #DBE1E8;
  border-bottom: 1px solid #DBE1E8;
  word-break: break-all;
}
.information-details table tbody tr td:last-child {
  border-right: none;
}
.information-explain {
  padding-top: 26px;
  border-bottom: 1px solid #DBE1E8;
}
.information-explain > p {
  line-height: 26px;
  font-size: 14px;
  color: #9EA7B3;
}
.information-explain .information-explain-tag {
  padding: 12px 0px 40px;
}
.information-explain .information-explain-tag a {
  float: left;
  height: 18px;
  line-height: 16px;
  font-size: 12px;
  color: #5E6D81;
  padding: 0px 7px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  margin: 0px 10px 10px 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.information-explain .information-explain-tag a:hover {
  color: #FF6666;
  border-color: #FF6666;
}
.prve-next-news {
  padding: 24px 0px;
}
.prve-next-news > ul > li {
  position: relative;
  line-height: 24px;
  font-size: 14px;
  color: #FF6666;
  padding: 10px 0px 10px 90px;
}
.prve-next-news > ul > li > span {
  position: absolute;
  left: 0px;
  top: 10px;
}
.prve-next-news > ul > li a {
  display: inline;
  font-size: 14px;
  color: #FF6666;
}
.prve-next-news > ul > li a:hover {
  text-decoration: underline;
}
.relevant-read-box .relevant-read-box-title {
  height: 60px;
  line-height: 60px;
  font-size: 20px;
  color: #2D3037;
  border-bottom: 1px solid #DBE1E8;
}
.relevant-read-box .relevant-read-box-title span {
  display: inline-block;
  height: 100%;
  border-bottom: 1px solid #FF6666;
  vertical-align: top;
}
.relevant-read-box .relevant-read-box-list {
  padding-top: 14px;
}
.relevant-read-box .relevant-read-box-list > ul {
  list-style-type: decimal;
  color: #5E6D81;
  padding-left: 20px;
}
.relevant-read-box .relevant-read-box-list > ul > li {
  line-height: 24px;
  font-size: 16px;
  padding: 10px 0px;
}
.relevant-read-box .relevant-read-box-list > ul > li a {
  color: #5E6D81;
}
.relevant-read-box .relevant-read-box-list > ul > li a:hover {
  color: #FF6666;
}
/*  paging */
.news-page {
  margin: 59px 0px 0px 0px;
  text-align: right;
}
.news-page > div {
  display: inline-block;
}
.news-page .pre-page,
.news-page .next-page {
  width: 76px;
  height: 37px;
  line-height: 37px;
  text-align: center;
  border: 1px solid #dddddd;
  vertical-align: middle;
}
.news-page .pre-page a,
.news-page .next-page a {
  color: #5E6D81;
}
.news-page .pre-page span {
  font-family: Arial;
  margin-right: 2px;
}
.news-page .next-page span {
  font-family: Arial;
  margin-left: 2px;
}
.news-page .page-list {
  vertical-align: middle;
}
.news-page .page-list ul li {
  float: left;
  margin-left: 10px;
}
.news-page .page-list ul li a {
  display: block;
  width: 35px;
  height: 35px;
  line-height: 37px;
  border: 1px solid #dddddd;
  text-align: center;
  color: #989b9a;
}
.news-page .page-list ul li a.morehidden {
  border: none;
  color: #989b9a;
}
.news-page .page-list ul li.active-page a {
  color: #FF6666;
  border-color: #FF6666;
}
.news-page .page-list ul li.active-page a.morehidden {
  color: #989b9a;
}
/* =========== system maintenance end =============*/
.information-details ul li p,
.information-details ol li p {
  display: inline;
}
.information-details {
  line-height: 30px;
  font-size: 16px;
  color: #2D3037;
}
.line {
  width: auto;
  height: auto;
  line-height: 24px;
  background-color: transparent;
  margin: 0px;
}
.line code {
  background-color: #F0F2F5;
  padding: 2px 4px;
}
/* =========== zixun xiangqingye fenxiang gongneng start =============*/
.information .content {
  position: relative;
}
.information .content .share-box {
  position: fixed;
  top: 150px;
  left: calc((100% - 1200px) / 2 - 85px);
  width: 50px;
  z-index: 999;
}
.share-box .like {
  position: relative;
  display: block;
  width: 50px;
  height: 80px;
  background-color: #fff;
  text-align: center;
  padding-top: 44px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.share-box .like .like-icon {
  position: absolute;
  left: 50%;
  margin-left: -10px;
  top: 15px;
  width: 20px;
  height: 20px;
  background: url(../../images/share-sprite.png) no-repeat -149px 0px;
}
.share-box .like .like-math {
  line-height: 24px;
  font-size: 14px;
  color: #FF6666;
}
.share-box .like:hover .like-icon,
.share-box .like.active .like-icon {
  background: url(../../images/share-sprite.png) no-repeat -179px 0px;
}
.share-box .share-item-box {
  width: 50px;
  background-color: #fff;
  padding: 0px 10px;
  margin-top: 20px;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
}
.share-box .share-item-box .share-tit {
  line-height: 54px;
  font-size: 14px;
  color: #5E6D81;
}
.share-box .share-item-box .share-item {
  position: relative;
  height: 60px;
  border-top: 1px solid #F0F2F5;
}
.share-box .share-item-box .share-item a {
  display: block;
  width: 100%;
  height: 100%;
  text-align: center;
}
.share-box .share-item-box .share-item .bubble {
  position: absolute;
  right: 60px;
  top: 50%;
  margin-top: -20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #5E6D81;
  background-color: #fff;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0px 12px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  display: none;
}
.share-box .share-item-box .share-item .bubble .arrow {
  content: "";
  position: absolute;
  top: 50%;
  right: -10px;
  margin-top: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-right-width: 0;
  border-left-color: #DBE1E8;
}
.share-box .share-item-box .share-item .bubble .arrow i {
  position: absolute;
  right: 1px;
  bottom: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-right-width: 0;
  border-left-color: #fff;
}
.share-box .share-item-box .share-item.wechat-share-item .wechat-icon {
  display: inline-block;
  width: 20px;
  height: 17px;
  background: url(../../images/share-sprite.png) no-repeat -34px -1px;
  margin-top: 22px;
  vertical-align: top;
}
.share-box .share-item-box .share-item.wechat-share-item:hover .wechat-icon {
  background: url(../../images/share-sprite.png) no-repeat -34px -30px;
}
.share-box .share-item-box .share-item.wechat-share-item .bubble {
  height: 160px;
  padding: 13px 22px 0px;
  margin-top: -80px;
}
.share-box .share-item-box .share-item.wechat-share-item .bubble img {
  display: block;
  width: 100%;
  height: 110px;
}
.share-box .share-item-box .share-item.wechat-share-item .bubble p {
  line-height: 34px;
  font-size: 14px;
  color: #5E6D81;
}
.share-box .share-item-box .share-item.qq-share-item .qq-icon {
  display: inline-block;
  width: 16px;
  height: 19px;
  background: url(../../images/share-sprite.png) no-repeat -64px 0px;
  margin-top: 21px;
  vertical-align: top;
}
.share-box .share-item-box .share-item.qq-share-item:hover .qq-icon {
  background: url(../../images/share-sprite.png) no-repeat -64px -29px;
}
.share-box .share-item-box .share-item.weibo-share-item .weibo-icon {
  display: inline-block;
  width: 20px;
  height: 17px;
  background: url(../../images/share-sprite.png) no-repeat -90px -1px;
  margin-top: 22px;
  vertical-align: top;
}
.share-box .share-item-box .share-item.weibo-share-item:hover .weibo-icon {
  background: url(../../images/share-sprite.png) no-repeat -90px -30px;
}
.share-box .share-item-box .share-item.copy-share-item .copy-icon {
  display: inline-block;
  width: 19px;
  height: 19px;
  background: url(../../images/share-sprite.png) no-repeat -120px 0px;
  margin-top: 22px;
  vertical-align: top;
}
.share-box .share-item-box .share-item.copy-share-item:hover .copy-icon {
  background: url(../../images/share-sprite.png) no-repeat -120px -29px;
}
.share-box .share-item-box .share-item:hover .bubble {
  display: block;
}
/* =========== zixun xiangqingye fenxiang gongneng end =============*/
/*========== 2020-06-24 code tag css style start ==========*/
.information-details p > code {
  font-size: 14px;
  color: #FF6666;
  background-color: #FFE0E0;
  padding: 2px 6px;
  border: 1px solid #DBE1E8;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 2px;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  margin: 0px 2px;
}
.information-details table tbody tr td {
  background-color: #f0f2f5;
}
.information-details table tbody tr td code {
  white-space: normal;
  color: #2D3037;
  background-color: transparent;
  border: none;
}
.information-details table tbody tr td code span {
  font-size: 13px;
  line-height: 200%;
  font-family: Menlo;
}
.information-details table tbody tr td p {
  margin: 0px;
}
/*========== 2020-06-24 code tag css style end ==========*/

/*========== 2020-09-26 zixun detail page guanggao start ==========*/
.relevant-read-box-title h2 {
  font-size: 20px;
}
.yisu-qa-advertising {
  position: relative;
  height: 120px;
  padding-left: 250px;
  border: 1px solid #DBE1E8;
  margin-top: 20px;
  margin-bottom: 39px;
}
.yisu-qa-advertising:hover {
  border-color: #FF6666;
}
.yisu-qa-advertising .advertising-img {
  position: absolute;
  left: -1px;
  top: -1px;
  width: 250px;
  height: 120px;
}
.yisu-qa-advertising .advertising-img a {
  display: block;
  width: 100%;
  height: 100%;
}
.yisu-qa-advertising .advertising-img a img {
  width: 100%;
  height: 100%;
}
.yisu-qa-advertising .advertising-text {
  height: 100%;
}
.yisu-qa-advertising .advertising-text a {
  display: block;
  height: 100%;
  padding: 17px 162px 0px 32px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.yisu-qa-advertising .advertising-text a div {
  line-height: 30px;
  font-size: 20px;
  color: #2D3037;
}
.yisu-qa-advertising .advertising-text a p {
  line-height: 26px;
  font-size: 14px;
  color: #5E6D81;
  margin-top: 7px;
}
.yisu-qa-advertising .advertising-text a .advertising-link {
  position: absolute;
  right: 32px;
  top: 40px;
  width: 120px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  font-size: 14px;
  color: #FF6666;
  background-color: #fff;
  border: 1px solid #FF6666;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;
}
.yisu-qa-advertising .advertising-text a .advertising-link:hover {
  background-color: #FFE0E0;
}
/*========== 2020-09-26 zixun detail page guanggao end ==========*/
/*========== 2020-10-30 zixun new nav start ==========*/
.qa-main-nav {
  position: absolute;
  left: 0px;
  top: 40px;
  width: 100%;
  height: 70px;
  background-color: transparent;
  border-top: 1px solid transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  z-index: 999;
}
.qa-main-nav:hover,
.qa-main-nav.qa-main-nav-hover {
  background-color: #323A45;
  border-top-color: rgba(255, 255, 255, 0.15);
  border-bottom-color: transparent;
}
.qa-main-nav .main-nav-center {
  width: 1200px;
  height: 100%;
  margin: 0 auto;
}
.qa-main-nav .main-nav-center .qa-main-tit {
  position: relative;
  float: left;
  height: 100%;
  line-height: 68px;
  font-size: 24px;
  color: #FFFFFF;
  padding: 0px 50px 0px 46px;
}
.qa-main-nav .main-nav-center .qa-main-tit > span {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -14px;
  width: 24px;
  height: 28px;
  background: url(../../images/zixun-icon.png) no-repeat center;
}
.qa-main-nav .main-nav-center .qa-main-tit > i {
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -12px;
  width: 1px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.3);
}
.qa-main-nav .main-nav-center .qa-main-link {
  float: left;
  height: 100%;
  margin-left: 40px;
}
.qa-main-nav .main-nav-center .qa-main-link > ul {
  height: 100%;
}
.qa-main-nav .main-nav-center .qa-main-link > ul > li {
  float: left;
  height: 69px;
  margin-right: 20px;
}
.qa-main-nav .main-nav-center .qa-main-link > ul > li > a {
  display: block;
  height: 100%;
  line-height: 70px;
  font-size: 18px;
  color: #FFFFFF;
  padding: 0px 10px;
  border-bottom: 2px solid transparent;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.qa-main-nav .main-nav-center .qa-main-link > ul > li.active a {
  color: #FF6666;
}
.qa-main-nav .main-nav-center .qa-main-link > ul > li:hover a {
  color: #FF6666;
  border-bottom-color: #FF6666;
}
.qa-main-nav .main-nav-center .qa-main-search {
  position: relative;
  float: right;
  padding-top: 16px;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box {
  position: relative;
  width: 280px;
  height: 36px;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box input[type=text] {
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #2D3037;
  background-color: #39424F;
  border: 1px solid #39424F;
  padding-right: 45px;
  padding-left: 16px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box input[type=text]:focus {
  background-color: #fff;
  border-color: #FFF;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box input[type=text]::-webkit-input-placeholder {
  font-size: 12px;
  color: #9EA7B3;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box input[type=text]::-moz-placeholder {
  font-size: 12px;
  color: #9EA7B3;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box input[type=text]::-ms-input-placeholder {
  font-size: 12px;
  color: #9EA7B3;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box .search-icon {
  position: absolute;
  right: 18px;
  top: 8px;
  width: 20px;
  height: 20px;
  background: url(../../images/qa-sprite.png) no-repeat -40px 0px;
  outline: none;
  border: none;
  cursor: pointer;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-box.active .search-icon {
  background: url(../../images/qa-sprite.png) no-repeat -70px 0px;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list {
  position: absolute;
  left: 0px;
  top: 54px;
  width: 100%;
  max-height: 320px;
  background-color: #fff;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0px 2px 8px 0px #eeeeee;
  -moz-box-shadow: 0px 2px 8px 0px #eeeeee;
  -o-box-shadow: 0px 2px 8px 0px #eeeeee;
  box-shadow: 0px 2px 8px 0px #eeeeee;
  overflow-y: auto;
  display: none;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #fff;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #fff;
  background-color: #F6F8F9;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #DBE1E8;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list::-webkit-scrollbar-thumb:hover {
  background-color: #DBE1E8;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list > ul > li > a {
  display: block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #2D3037;
  padding-left: 16px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.qa-main-nav .main-nav-center .qa-main-search .main-search-list > ul > li > a:hover {
  color: #FF6666;
  background-color: #F0F2F5;
}
/*========== 2020-10-30 zixun new nav end ==========*/
/*========== 2020-10-30 zixun new change start ==========*/
.information-right-recommend {
  margin-top: 40px;
}
.zixun-secondary-nav {
  height: 60px;
  border-bottom: 1px solid #DBE1E8;
}
.zixun-secondary-nav > ul {
  width: 1200px;
  height: 60px;
  margin: 0 auto;
}
.zixun-secondary-nav > ul > li {
  float: left;
}
.zixun-secondary-nav > ul > li a {
  display: block;
  height: 60px;
  line-height: 60px;
  font-size: 16px;
  color: #2D3037;
  padding: 0px 4px;
  border-bottom: 2px solid transparent;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  margin-right: 50px;
}
.zixun-secondary-nav > ul > li.active a {
  color: #FF6666;
  border-bottom-color: #FF6666;
}
.information-left-box > h1 {
  display: inline-block;
  line-height: 76px;
  font-size: 36px;
  color: #2D3037;
  padding-bottom: 20px;
}
.information-left-box > h1 span {
  color: #FF6666;
  margin-right: 12px;
}
.information .content .information-right-box .information-right-img {
  padding-top: 0px;
  margin-top: 20px;
}

.qa-yisu-nav,.qa-main-nav{
  position: relative;
  left: inherit;
  top: inherit;
  background-color: #323A45;
}
.qa-main-nav{
  border-top-color: rgba(255, 255, 255, 0.15);
  border-bottom-color: transparent;
}
/*========== 2020-10-30 zixun new change end ==========*/

/********************2020-12-18 new change  start *******************/
.information-right-box {
  position: relative;
}
.information-right-box h6 {
  width: 100%;
  height: 60px;
  line-height: 60px;
  font-size: 20px;
  color: #2D3037;
  border-bottom: 1px solid #DBE1E8;
}
.information-right-box h6 span {
  display: inline-block;
  border-bottom: 1px solid #FF6666;
}
.information-right-box .recommend-tab-list-item {
  padding-top: 20px;
}
.information-right-box .recommend-tab-list-item ul {
  list-style-type: decimal;
  list-style-position: inside;
  color: #5E6D81;
}
.information-right-box .recommend-tab-list-item ul li {
  line-height: 42px;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.information-right-box .recommend-tab-list-item ul li a {
  color: #5E6D81;
}
.information-right-box .recommend-tab-list-item ul li a:hover {
  color: #FF6666;
}
.information-right-box .latest-news {
  width: 320px;
  padding-bottom: 20px;
}
.information-right-box .relevant-nominate {
  width: 320px;
  padding-bottom: 20px;
}
/********************2020-12-18 new change end *******************/

.search-list .loading {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: none;
}
.search-list .loading img {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -16px;
  margin-top: -16px;
}

.search-list ul {
  min-height: 120px;
}

.search-list ul li a {
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.no-qa-results {
  text-align: center;
  line-height: 120px;
}

.keyword-result {
  color: #FF6666;
}

.pre-page {
  cursor: pointer;
}

.next-page {
  cursor: pointer;
}

.nav-show {
  background-color: #323A45 !important;
}

.news-page .pre-page a,
 .news-page .next-page a {
   display: block;
   width: 100%;
   height: 100%;
 }
.information .content .information-right-box .information-right-img {
  width: 320px;
  margin-top: 0;
}
.information .content .information-right-box .relevant-nominate {
  padding-bottom: 0;
}

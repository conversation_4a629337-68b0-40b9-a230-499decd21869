$.getJSON("https://yisuapi.yisu.com/index.php/Speed/Uc/doTryccSet/?callback=?",function(res){if(res>3){$('.verifycodeFirstnofast,.verifycodeFirstnofast_phone').show();}},'JSONP');
rowBinds = function () {
    var curError,
        UA = navigator.userAgent.toLowerCase(),
        isOpenQQBind = false,
        html;

    var _html = '';
    _html += '<div class="login-shdow" style="display:none;">';
    _html += '<div class="login-modal">';
    _html += '<div class="step-body">';
    _html += '<div class="step-title">绑定亿速云账号<span class="qq-close-btn" style="cursor:pointer;">×</span></div>';
    _html += '<div class="step-one" style="display: block;">';
    _html += '<p>如果您还没有注册亿速云账号，请</p>';
    _html += '<a href="javascript:;" id="goreg_bind">立即注册</a>';
    _html += '<p class="mt16">如果您已注册过亿速云账号，请</p>';
    _html += '<a href="javascript:;" id="gobind">立即绑定</a>';
    _html += '</div>';
    _html += '<div class="step-two" style="display:none;">';
    _html += '<form action="#" onsubmit="return false">';
    _html += '<input type="text" class="qq-dc-ipt" autocomplete="off" id="reg_cc" placeholder="请输入亿速云账号/手机号" maxlength="20" />';
    _html += '<input type="password" style="display:none;width:0px;height:0px;">';
    _html += '<input type="text" class="qq-dc-ipt" type="password" id="reg_psw"  placeholder="请输入密码" maxlength="20" onfocus="this.type=\'password\',this.autocomplete=\'new-password\'" autocomplete="off" />';
    _html += '<input type="text" style="z-index: -20;display: none;">';
    _html += '<div class="matchbox">';
    _html += '<input type="text" class="qq-dc-ipt" type="text" id="reg_code"  placeholder="请输入验证码"/><span><img src="https://yisuapi.yisu.com/index.php/Home/Index/verify/" alt="点击重新获取验证码" id="reg_code_img" alt=""/></span>';
    _html += '</div>';
    _html += '<div class="bind-login-error" style="line-height: 16px;margin-top: 20px;font-size: 16px;color: #FF6666;display: none;"></div>';
    _html += '<button type="submit"  class="qq-dc-bind" type="button" id="btnBinded" >绑定</button>';
    _html += '</form>';
    _html += '</div>';
    _html += '</div>';
    _html += '</div>';
    _html += '</div>';

// 点击立即绑定
    $(document).on('click', '#gobind', function(){
        $('.login-shdow .step-two').show();
        $('.login-shdow .step-one').hide();
    });

    $(document).on('click', '#goreg_bind', function(){
        $('.login-shdow,.log_box').hide();
        $('.reg_box').show();
        $('.log_reg_modal').show();
    });

    $(document.body).append( _html );
    $('#reg_code_img').on('click',resetCode);
    $('.qq-close-btn').click(function(){
        $('.login-shdow').hide();
    });
    $(".log-regs-mod").click(function(e){
        if(e && e.stopPropagation){
            e.stopPropagation();
        }else{
            window.event.cancelBubble = true;
        }
    });;

    var ms = Date.parse(new Date()) + 1000 * 60 * 60 * 1, expires = new Date(ms);

    var login_in = '';
    var cookies = document.cookie ? document.cookie.split('; ') : [];
    for (var i = 0, l = cookies.length; i < l; i++) {
        var parts = cookies[i].split('=');
        var name = parts.shift();
        var value = parts.join('=');
        if( 'previousUrl'==name ) login_in = value;
    }

    $('#reg_code').keydown(function(event){
        if(event.keyCode == 13) $('#btnBinded').click();
    });

    window.getScript = function(url){
        $.getScript(url);
    };

    var isFirst = false;
    window.openDiv = function(){
        if( !isFirst ){
            createPlaceholder('reg_cc','亿速云账号/手机号',18,45);
            createPlaceholder('reg_psw','亿速云密码',18,45);
            createPlaceholder('reg_code','验证码',18,45);
            isFirst = true;
        }
        resetCode();

        $(".login-shdow .qq-dc-cms-con").css({
            top: ($(window).scrollTop() + 150) + "px"
        });
        $(".login-shdow").css({
            height: (window.innerHeight) + "px"
        });
        $('.step-two').hide();
        $('.step-one').show();
        $('.log_reg_modal').hide();
        $(".login-shdow").show(300);
        $('#reg_cc,#reg_psw,#reg_code').val('');
    };

    $("#btnReg").on('click',function(){
        window.location.href = "/index/reg/";
    });

    $("#btnBind").on('click',function(){
        $("#choose").hide();
        $("#reg").hide();
        $("#bind").show();
        isOpenQQBind = true;
    });

    //绑定已有账号
    $(document).on('click','#btnBinded',function () {
        $('.bind-login-error').text('').hide();
        var reg_cc = $.trim( $('#reg_cc').val() ),
            reg_psw = $.trim( $('#reg_psw').val() ),
            reg_code = $.trim( $('#reg_code').val() );

        if( ''==reg_cc ){
            $('.bind-login-error').text('请输入亿速云账号').show();
            $('#reg_cc').focus();
            return false;
        }
        if( ''==reg_psw ){
            $('.bind-login-error').text('请输入密码').show();
            $('#reg_psw').focus();
            return false;
        }
        if( ''==reg_code ){
            $('.bind-login-error').text('请输入验证码').show();
            $('#reg_code').focus();
            return false;
        }

        var url = "https://yisuapi.yisu.com/index.php/Speed/Uc/loginbind";

        $.ajax({
            type: "POST",
            dataType: "json",
            url: url,
            data: {name:reg_cc, password: reg_psw, code: reg_code},
            xhrFields: { withCredentials: true },
            cache: false,
            async: false,
            success: function(data)
            {
                if( 0 == data.error ) {
                    $('.bind-login-error').text('绑定亿速云账号成功！').show();
                    document.cookie = "previousUrl=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
                    setTimeout(function(){
                        $(".login-shdow").hide(300);
                        window.location.reload();
                    },1500);
                } else if( 2 == data.error ) {
                    resetCode();
                    $('.bind-login-error').text('验证码错误，请重新输入！').show();
                    return false;
                } else {
                    $('.bind-login-error').text(data.msg).show();
                    return false;
                }
            }
        });
    });

    $("#btnBack").on('click',function(){
        $("#choose").show();
        $("#reg").hide();
        $("#bind").hide();
        isOpenQQBind = false;
    });
}

rowLogin = function () {
    isprobation = false;
    $('#submit-login').on('click',function () {
        var obj_user = $('#login-username'),
            obj_pwd  = $('#login-password'),
            str_user = $.trim(obj_user.val()),
            str_pwd  = $.trim(obj_pwd.val());
        var verifycode = $("#verifycode").val();
        islogin = true;

        if (str_user.length < 3) {
            $('#login-error').html('请输入用户名！').css({"margin-bottom":"20px"});
            obj_user.focus();
            $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
            return false;
        }else{
            $('#login-error').html('').css({"margin-bottom":"0"});
        }
        if (str_pwd.length < 6) {
            $('#login-error').html('请输入密码！').css({"margin-bottom":"20px"});
            obj_pwd.focus();
            $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
            return false;
        }else{
            $('#login-error').html('').css({"margin-bottom":"0px;"});
        }

        var trydo = $.getJSON("https://yisuapi.yisu.com/index.php/Speed/Uc/doTrycc/?callback=?",function(res){window.doTry = res.data;},'JSONP');
        str_user = window.btoa(str_user);
        str_pwd  = window.btoa(str_pwd);

        //qyloading.show('#win-login', '登录中，请稍候...');
        $('#submit-login').text('登录中，请稍候...');

        $.when(trydo).done(function () {
            var url = "https://yisuapi.yisu.com/index.php/Speed/Uc/doLogin?callback=?",
                data = "name="+ encodeURIComponent(window.btoa(str_user)) +"&password="+ encodeURIComponent(window.btoa(str_pwd)) +"&md5=0" + "&nowtimes=" +  window.btoa(new Date().getTime()) + "&cctry=" + encodeURIComponent(window.doTry) + "&verifycode=" + encodeURIComponent(verifycode) + "&ishow=" + encodeURIComponent('ishow');

            $.ajax({
                type: "get",
                dataType: 'jsonp',
                url: url,
                data: data,
                timeout: 30000,
                xhrFields: { withCredentials: true },
                async: false,
                cache: false,
                success: function (dt) {
                    if (dt.status * 1 == 1) {
                        if(isprobation){
                            $('#submit-login').text('登录成功');
                            $('.log_reg_modal').hide();
                        }else {
                            if(window.location.href.indexOf("/cloud/high_selfconf") > 0){
                                $('#submit-login').text('登录成功');
                                $('.log_reg_modal').hide();
                            }else{
                                $('#submit-login').text('登录成功，正在跳转至订单确认页...');
                            }
                        }
                        $('.btnSubmit').trigger('click');
                        $('#username').html(dt.data.username);
                        $('.usercenter,.mtusercenter').attr('href','https://uc.yisu.com/vhost');
                        $('#recoverd').attr('href','javascript:;');
                        $('.yisu-login,.userbox-login,.login-reg-box').hide();
                        $('#login-error').html('').css({"margin-bottom":"0px;"});
                        $('#user_info,.userbox').show();
                    } else {
                        if(dt['msg']==''){
                            $('#login-error').html('').css({"margin-bottom":"0px"});
                        }else{
                            $('#login-error').html(dt['msg']+' 请重新登录').css({"margin-bottom":"20px"});
                        }
                        $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
                        $('#submit-login').text('登录');
                        if('display'==dt.data){
                            $('.verifycodeFirstnofast').show();
                            $('#code_img_kj').trigger('click');
                        }else{
                            $('.verifycodeFirstnofast').hide();
                        }
                        return false;
                    }
                },
                error: function () {
                    $('#login-error .err').html('网络错误，请稍后再试！').css({"margin-bottom":"20px"});
                    $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
                    $('#submit-login').text('登录');
                    return false;
                }
            });
        });
    });

    $('#code_img_kj').on('click',function () {
        $(this).attr('src', 'https://yisuapi.yisu.com/Home/Index/verifyNoAgo?' + Math.random());
        $('#verifycode').val('');
    });
};

rowLoginFast = function () {
    $.getJSON("https://yisuapi.yisu.com/index.php/Home/Index/goFastNumSet/?callback=?",function(res){$('#tokens').val(res);},"JSONP");
    $('#submit-login-fast').on('click',function () {
        var conven_phone_fast = $.trim($('#login_phone_conven_fast').val());
        var conven_code_fast = $.trim($('#conven_code_fast').val());
        var fastGologin = false;

        if(conven_phone_fast == ''){
            $('#login-error').html('请输入手机号！').css({"margin-bottom":"20px"});
            $('#login_phone_conven_fast').focus();
            $('#login_phone_conven_fast,#conven_code_fast,#verifycode_fast').val('');
            return false;
        }
        if(!/^(((13[0-9]{1})|(15[0-9]{1})|(14[1-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(conven_phone_fast)){
            $('#login-error').html('手机号格式不正确！').css({"margin-bottom":"20px"});
            $('#login_phone_conven_fast').focus();
            $('#login_phone_conven_fast,#conven_code_fast,#verifycode_fast').val('');
            return false;
        }

        if(conven_code_fast == ''){
            $('#login-error').html('请输入短信验证码！').css({"margin-bottom":"20px"});
            $('#conven_code_fast').focus();
            $('#conven_code_fast,#verifycode_fast').val('');
            return false;
        }
        if(conven_code_fast.length != 6){
            $('#login-error').html('短信验证码不正确！').css({"margin-bottom":"20px"});
            $('#conven_code_fast').focus();
            $('#conven_code_fast,#verifycode_fast').val('');
            return false;
        }

        $.ajax({
            type: 'POST',
            url : 'https://yisuapi.yisu.com/index.php/Speed/Uc/checkSmsCode',
            dataType: 'json',
            async: false,
            data: {code:conven_code_fast},
            xhrFields: { withCredentials: true },
            success:function(res){
                if(res == -4){
                    $('#login-error').html('对不起，您还没有获取短信验证码').css({"margin-bottom":"20px"});
                    $('#conven_code_fast').focus();
                    $('#conven_code_fast,#verifycode_fast').val('');
                }else if(res == -3){
                    $('#login-error').html('短信验证码已输错三次').css({"margin-bottom":"20px"});
                    $('#conven_code_fast').focus();
                    $('#conven_code_fast,#verifycode_fast').val('');
                }else if(res == -1){
                    $('#login-error').html('短信验证码已超时失效,请重新获取').css({"margin-bottom":"20px"});
                    $('#conven_code_fast').focus();
                    $('#conven_code_fast,#verifycode_fast').val('');
                }else if(res == 0){
                    $('#login-error').html('短信验证码错误').css({"margin-bottom":"20px"});
                    $('#conven_code_fast').focus();
                    $('#conven_code_fast,#verifycode_fast').val('');
                }else{
                    $('#login-error').html('').css({"margin-bottom":"0px"});
                    fastGologin = true;
                }
            }
        });

        if(!fastGologin) return false;
        var url = "https://yisuapi.yisu.com/index.php/Speed/Uc/doLoginFastNew?callback=?", data = "name="+ encodeURIComponent(conven_phone_fast) + "&code=" + encodeURIComponent(conven_code_fast) + "&nowtimes=" + new Date().getTime() + "&ishow="+ encodeURIComponent('ishow');
        $('#submit-login-fast').text('正在跳转...');

        /* url传跳转过来地址，然后页面登录后，再返回那个地址 */
        var href = window.location.href, match = href.match(/\?from=(.+?)$/);
        if(match && match[1]) login_in = match[1];
        $.ajax({
            type: "get",
            url: url,
            dataType: "jsonp",
            data: data,
            timeout: 30000,
            xhrFields: { withCredentials: true },
            cache: false,
            success: function(data) {
                if( 1==data.status ) {
                    $('#submit-login-fast').text('登录成功，正在跳转至订单确认页...');
                    $('.btnSubmit').trigger('click');
                    $('#username').html(data.data.username);
                    $('.usercenter,.mtusercenter').attr('href','https://uc.yisu.com/vhost');
                    $('#recoverd').attr('href','javascript:;');
                    $('.yisu-login,.userbox-login,.login-reg-box,.log_reg_modal').hide();
                    $('#login-error').html('').css({"margin-bottom":"0px;"});
                    $('#user_info,.userbox').show();
                }else{
                    $('#login-error').html(data['msg']+' 请重新登录').css({"margin-bottom":"20px"});
                    $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
                    $('#submit-login-fast').text('登 录');
                    if('display'==dt.data){
                        $('.verifycodeFirstnofast_phone').show();
                        $('#code_img_kj_fast').trigger('click');
                    }else{
                        $('.verifycodeFirstnofast_phone').hide();
                    }
                    return false;
                }
            },
            error: function(){
                $('#submit-login-fast').text('登 录');
                $('#login-password,#verifycode,#conven_code_fast,#verifycodeFirstnofast_phone').val('');
                $('.login-error').html('').css({"margin-bottom":"0px"});
                return false;
            }
        });
    });

    $('.sendBtn_cp_fast').on('click',function () {
        var cikfs = $('#code_img_kj_fast').trigger('click');
        var fastGologin_cp = false;
        $.when(cikfs).done(function () {
            var conven_phone_fast = $.trim($('#login_phone_conven_fast').val());
            var conven_code_fast = $.trim($('#conven_code_fast').val());

            if(conven_phone_fast == ''){
                $('#login-error').html('请输入手机号！').css({"margin-bottom":"20px"});
                $('#login_phone_conven_fast').focus();
                $('#login_phone_conven_fast').val('');
                $('#conven_code_fast,#verifycode_fast').val('');
                return false;
            }
            if(!/^(((13[0-9]{1})|(15[0-9]{1})|(14[1-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(conven_phone_fast)){
                $('#login-error').html('手机号格式不正确！').css({"margin-bottom":"20px"});
                $('#login_phone_conven_fast').focus();
                $('#login_phone_conven_fast').val('');
                $('#conven_code_fast,#verifycode_fast').val('');
                return false;
            }
            setTimeout(function () {
                $.ajax({
                    type: 'GET',
                    url : 'https://yisuapi.yisu.com/index.php/Speed/Uc/isExistsTelLogin?callback=?',
                    dataType: 'jsonp',
                    data: {phone:conven_phone_fast},
                    async: false,
                    success:function(res){
                        if(res == 1){
                            if(!$('.sendBtn_cp_fast').hasClass("sent")){
                                timei=100;
                                $('.sendBtn_cp_fast').addClass("sent");
                                remainningTime_cp_fast ();
                                timeri=setInterval(remainningTime_cp_fast,1000);
                                $('#conven_code_fast').focus();
                                var tokensl =  $('#tokens').val();
                                $.ajax({
                                    type: 'GET',
                                    url : 'https://yisuapi.yisu.com/index.php/Speed/Uc/getSmsCodeReg?callback=?',
                                    dataType: 'jsonp',
                                    data: {phone:conven_phone_fast,code:tokensl,type:'fastYisuLognPhone'},
                                    success:function(res){
                                        if(res == 1){
                                            issend = true;
                                            $('#conven_code_fast').focus();
                                            $('#login-error').html('').css({"margin-bottom":"0px"});
                                            return false;
                                        }else if(res == 0){
                                            $('#login-error').html('短信验证码发送失败，请稍候重新获取').css({"margin-bottom":"20px"});
                                            return false;
                                        }else if(res == -1){
                                            $('#login-error').html('请不要频繁获取短信验证码').css({"margin-bottom":"20px"});
                                            return false;
                                        }else if(res == -2){
                                            $('#login-error').html('手机号码不能为空').css({"margin-bottom":"20px"});
                                            return false;
                                        }else if(res == -3){
                                            $('#login-error').html('手机号码格式不正确').css({"margin-bottom":"20px"});
                                            return false;
                                        }
                                        if(!issend){
                                            timei = 0;
                                            $(".sendBtn_cp_fast").removeClass("sent").html("重新获取");
                                            $('#login-error').html('').css({"margin-bottom":"0px"});
                                        }
                                    }
                                });
                            }
                        }else{
                            $('#login-error').text('').css({"margin-bottom":"0px"});
                        }
                    }
                });
            },500);
        });
    });
}

rowSwitch = function () {
    $('#login_phone_conven_fast').keydown(function(event){
        if(event.keyCode == 13){
            $('.sendBtn_cp_fast')[0].click();
        }
    });

    $('#reg-phone').keydown(function(event){
        if(event.keyCode == 13){
            $('.sendBtn_reg_fast')[0].click();
        }
    });

    $('#login-username,#login-password,#verifycode').keydown(function(event){
        if(event.keyCode == 13){
            $('#submit-login').click();
        }
    });

    $('#login_phone_conven_fast,#conven_code_fast,#verifycode_fast').keydown(function(event){
        if(event.keyCode == 13){
            $('#submit-login-fast').click();
        }
    });

    $('#reg-verify').keydown(function(event){
        if(event.keyCode == 13){
            $('#submit-reg-a').click();
        }
    });
    $('#reg-step-b-code').keydown(function(event){
        if(event.keyCode == 13){
            $('#submit-reg-b').click();
        }
    });
    $('.login-register .closebtn').click(function () {
        $('.login-register').hide();
    });
};

rowRegister = function () {
    var regerror = {};
    var reguser  = {};
    islogin = false;
    $('#win-register .err').hide();
    $('#win-register .ipt').val('');
    $('#reg-phone').blur(function () {
        var obj = $(this), v = $.trim(obj.val()),
            re               = /^(((13[0-9]{1})|(15[0-9]{1})|(14[1-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/;
        if (!re.test(v)) {
            regerror['phone'] = 1;
            $('#login-error-reg').html('请输入正确的手机号码！');
            return false;
        }
        $.get('https://yisuapi.yisu.com/index.php/Speed/Uc/isExistsTelreg', {phone: v}, function (res) {
            if (res * 1 == 1) {
                regerror['phone'] = 1;
                $('#login-error-reg').html('该手机号已经注册，请在右下脚点击前往登录进行登录验证');
                return false;
            }
            regerror['phone'] = 0;
            $('#login-error-reg').html('');
        },'jsonp');
    });
    /*$('#reg-password').blur(function () {
        var obj = $(this), v = $.trim(obj.val()),
            re               = /^.{6,20}$/;
        if (!re.test(v)) {
            regerror['password'] = 1;
            $('#reg-password-err').text('密码不合法，6-20个字符').show();
            return false;
        }
        regerror['password'] = 0;
        $('#reg-password-err').hide();
    });*/
    $('#verifycode_fast_reg').blur(function () {
        var obj = $(this), v = $.trim(obj.val()),
            re               = /^.{5}$/;
        if (!re.test(v)) {
            regerror['verify'] = 1;
            $('#login-error-reg').html('验证码错误');
            return false;
        }
        regerror['verify'] = 0;
        $('#login-error-reg').html('');
    });


    $('.sendBtn_reg_fast').on('click',function () {
        var cikrs = $('#code_img_kj_reg').trigger('click');
        var fastGoReg_cp = false;
        $.when(cikrs).done(function () {
            var reg_phone = $.trim($('#reg-phone').val());
            var reg_step = $.trim($('#reg-step-b-code').val());

            if(reg_phone == ''){
                $('#login-error-reg').html('请输入正确的手机号码！');
                $('#reg-phone').focus();
                $('#reg-phone,#reg-step-b-code,#verifycode_fast_reg').val('');
                return false;
            }
            if(!/^(((13[0-9]{1})|(15[0-9]{1})|(14[1-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(reg_phone)){
                $('#login-error-reg').html('手机号格式不正确！');
                $('#reg-phone').focus();
                $('#reg-phone,#reg-step-b-code,#verifycode_fast_reg').val('');
                return false;
            }
            setTimeout(function () {
                $.get('https://yisuapi.yisu.com/index.php/Speed/Uc/isExistsTelreg', {phone: reg_phone}, function (res) {
                    if (res * 1 == 1) {
                        regerror['phone'] = 1;
                        $('#login-error-reg').html('该手机号已经注册，请在右下脚点击前往登录进行登录验证');
                        return false;
                    }else{
                        regerror['phone'] = 0;
                        $('#login-error-reg').html('');

                        if(!$('.sendBtn_reg_fast').hasClass("sent")){
                            timeir=100;
                            $('.sendBtn_reg_fast').addClass("sent");
                            remainningTime_reg_fast();
                            timerir=setInterval(remainningTime_reg_fast,1000);
                            $('#reg-step-b-code').focus();
                            var tokens_reg =  $('#tokens').val();
                            $.ajax({
                                type: 'GET',
                                url : 'https://yisuapi.yisu.com/index.php/Speed/Uc/getSmsCodeReg?callback=?',
                                dataType: 'jsonp',
                                data: {phone:reg_phone,type:'reg',code:tokens_reg,regs:'3c65f2d3322b92c56'},
                                success:function(res){
                                    if(res == 1){
                                        issend = true;
                                        $('#reg-step-b-code').focus();
                                        $('#login-error-reg').html('');
                                        return false;
                                    }else if(res == 0){
                                        $('#login-error-reg').html('短信验证码发送失败，请稍候重新获取');
                                        return false;
                                    }else if(res == -1){
                                        $('#login-error-reg').html('请不要频繁获取短信验证码');
                                        return false;
                                    }else if(res == -2){
                                        $('#login-error-reg').html('手机号码不能为空');
                                        return false;
                                    }else if(res == -3){
                                        $('#login-error-reg').html('手机号码格式不正确');
                                        return false;
                                    }
                                    if(!issend){
                                        timeir = 0;
                                        $(".sendBtn_reg_fast").removeClass("sent").html("重新获取");
                                        $('#login-error-reg').html('');
                                    }
                                }
                            });
                        }

                    }

                },'jsonp');
            },500);
        });
        return false;
    });

    $('#submit-reg-b').on('click',function(){
        var reg_phone = $.trim($('#reg-phone').val()),v = $.trim($('#reg-step-b-code').val()),verifycodereg = $.trim($('#verifycode_fast_reg').val()),checkcode = false;

        if(reg_phone == ''){
            $('#login-error-reg').html('请输入正确的手机号码！');
            $('#reg-phone').focus();
            $('#reg-phone,#reg-step-b-code,#verifycode_fast_reg').val('');
            return false;
        }
        if(!/^(((13[0-9]{1})|(15[0-9]{1})|(14[1-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$/.test(reg_phone)){
            $('#login-error-reg').html('手机号格式不正确！');
            $('#reg-phone').focus();
            $('#reg-phone,#reg-step-b-code,#verifycode_fast_reg').val('');
            return false;
        }

        if(v == ''){
            $('#login-error-reg').html('请输入验证码');
            $('#reg-step-b-code').focus();
            $('#reg-step-b-code,#verifycode_fast_reg').val('');
            return false;
        }
        if(v.length!=6){
            $('#login-error-reg').html('验证码不正确');
            $('#reg-step-b-code').focus();
            $('#reg-step-b-code,#verifycode_fast_reg').val('');
            return false;
        }

        if(!$('#agree_clause').is(':checked')){
            $('#login-error-reg').html('请点击同意《亿速云用户服务条款》');
            return false;
        }

        $('#login-error-reg').html('');
        checkcode = true;
        reguser['verifycode'] = verifycodereg;
        reguser['regphone']   = reg_phone;
        reguser['agree']      = 'isok';
        reguser['code']       = v;

        if(!checkcode) return false;
        $('#submit-reg-b').text('正在注册中...');

        $.ajax({
            type: 'POST',
            url : 'https://yisuapi.yisu.com/index.php/Speed/Uc/checkSmsCode',
            async: false,
            data: {code:v},
            dataType: 'json',
            timeout: 300000,
            xhrFields: { withCredentials: true },
            success: function(res){
                if(res == -4){
                    $('#submit-reg-b').text('注 册');
                    $('#login-error-reg').html('对不起，您还没有获取短信验证码');
                    $('#reg-step-b-code,#verifycode_fast_reg').val('');
                    checkcode = false;
                    return false;
                }else if(res == -3){
                    $('#submit-reg-b').text('注 册');
                    $('#login-error-reg').html('短信验证码已输错三次');
                    $('#reg-step-b-code,#verifycode_fast_reg').val('');
                    window.setTimeout(function(){
                        window.location.reload();
                    }, 1500);
                    return false;
                }else if(res == -1){
                    $('#submit-reg-b').text('注 册');
                    $('#login-error-reg').html('短信验证码已超时失效,请重新获取');
                    $('#reg-step-b-code,#verifycode_fast_reg').val('');
                    checkcode = false;
                    return false;
                }else if(res == 0){
                    $('#submit-reg-b').text('注 册');
                    $('#login-error-reg').html('短信验证码错误');
                    $('#reg-step-b-code,#verifycode_fast_reg').val('');
                    checkcode = false;
                    return false;
                }else{
                    checkcode = true;
                    $.ajax({
                        url: 'https://yisuapi.yisu.com/index.php/Speed/Uc/doregNewWapStep',
                        data: reguser,
                        type: 'POST',
                        dataType: 'json',
                        timeout: 30000,
                        xhrFields: { withCredentials: true },
                        success: function (res) {
                            if (res.status * 1 == 1) {
                                $('#submit-reg-b').text('注册成功，正在跳转至订单确认页...');
                                $('.btnSubmit').trigger('click');

                                // $('#log_box,#reg_box').hide();
                                // $('.log_reg_modal').hide();
                                // $('#reg_success').show();
                            } else {
                                $('#login-error-reg').html(res.msg+' 请重新注册');
                            }
                        },
                        error: function () {
                            $('#login-error-reg').html('网络错误，请稍后再试');
                            return false;
                        }
                    });
                }
            }
        });
    });

};

var countDown = function (id, second, callback) {
    second      = second || 100;
    var obj     = $(id);
    var timeObj = window.setInterval(function () {
        second--;
        if (second < 1) {
            window.clearInterval(timeObj);
            if (callback) callback();
        }
        obj.text(second);
    }, 1000);
    return timeObj;
};

//验证码
var timei ,timeri,timeir,timerir;
function remainningTime_cp_fast (){
    timei--;
    if(timei<=0){
        clearInterval(timeri);
        $(".sendBtn_cp_fast").removeClass("sent").html("重新获取");
    }else{
        $(".sendBtn_cp_fast").html("重新获取("+timei+"S)");
    }
}
function remainningTime_reg_fast (){
    timeir--;
    if(timeir<=0){
        clearInterval(timerir);
        $(".sendBtn_reg_fast").removeClass("sent").html("重新获取");
    }else{
        $(".sendBtn_reg_fast").html("重新获取("+timeir+"S)");
    }
}

function resetCode() {
    var url = "https://yisuapi.yisu.com/index.php/Home/Index/verify/r/" + Math.random();
    $('#reg_code_img').attr('src', url);
    $('#reg_code').val('');
}

function createPlaceholder(id,text,top,left){
    top = top || 0;
    left = left || 0;

    var obj = $('#' + id)[0];

    if( undefined!==obj.placeholder ){
        obj.placeholder = text;
        return;
    }

    var parent = obj.parentNode,
        clickEvent = function(){
            $(div)[0].style.visibility = 'hidden';
            var target = this.nextSibling;
            if( target.nodeName=='#text' ){
                target = target.nextSibling;
            }
            target.focus();
        },
        blurEvent = function(){
            var value = $.trim(this.value);
            if( value=='' )
            {
                $(div)[0].style.visibility = 'visible';
            }
        },
        focusEvent = function(){
            $(div)[0].style.visibility = 'hidden';
        };

    $(parent).css({position: 'relative'});

    var div = document.createElement('div');
    div.innerHTML = text;
    $(div).css({position: 'absolute', top: top, left: left, color: "#999",'font-size':'14px','z-index':'99999'});
    parent.insertBefore(div,obj);
    $(div).click(clickEvent);
    $(obj).blur(blurEvent);
    $(obj).focus(focusEvent);
}

$('#code_img_kj_reg,#code_img_kj,#code_img_kj_fast').on('click',function () {
    var ids = $(this).attr('id');
    var fast_code_set = $(this).attr('src', 'https://yisuapi.yisu.com/Home/Index/verifyNoAgo?' + Math.random());
    $.when(fast_code_set).done(function () {
        $('#verifycode').val('');
        $('#verifycode_fast').val('');
        $('#verifycode_fast_reg').val('');
        if(ids = 'code_img_kj_fast'){
            setTimeout(function () {
                $.getJSON("https://yisuapi.yisu.com/index.php/Home/Index/goFastNumSet/?callback=?",function(res){$('#tokens').val(res);},"JSONP");
            },200);
        }
    });
});

rowBinds();
rowLogin();
rowLoginFast();
rowRegister();
rowSwitch();

[{"description": "treehash per file", "signed_content": {"payload": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "BJ_4OAlaLWhdLjyDDUt8r7124kGxrx-HFuoicu-uD--psI_DQOVOeFEC1xjo0m10faU60aBEdkMBb3Z1l34CLag-3Xr4tCd8DdPTbSyRcLX-yHzRMfh1FA18bvNhieJJKJDUNGuZ4ZzpMZBXkdOF_NqL44TZxZOgYIm170KbCXrPA_7-9mENUXby0yzpxLLsjkYMmIYofF2iGFluKmT_SrGPjkVAE_z6JQ-IpSH4rg1LgzpMQNtUvI8O1SA_DXlRUnhskNALNR7VK468_phTKOX5HerFiL_PlPzrZ8VyiuWzQaEvcqQfxF789P0JWAmAV4Zanai9w1JYX9kOtQdK_w"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "DhQ-BBDK6FTFTkCmw9pOhTqjYlMTbNRZ0DlL1DbW96zILtu5AhMBi4p6BpvEkVxhdfhBiPlQcH7Y0gjvRqQpjzrWCj2NSoJyyzV-cO1n-SUzMs3RhsC30tNoPOu9Ntu5Y1NCe_JLBU_lE9rDkJqTB-PteUQHIzWkqibhePox9Z448YlSfj3cMhBkDZxzdyY2XcWOodi7kZM3moYPj8XK9ilceDYHJTI_b9icZKiEAuYqrJ2Gs_nNw5bkQzEMtNzEmLYMt3qrQoNirrkFjEUh_gXvVtmmyYYSF7nQJnVQ_U-w_bq0ttfZAa-AyN1AEFgaJVD2tTgMbh1LgfoKnOAVFA"}]}}]
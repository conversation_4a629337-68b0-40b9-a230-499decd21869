[{"description": "treehash per file", "signed_content": {"payload": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "rEEvU3HHKPCLYSnYYAY-rx5fuLs8JhVe4ggsq1f8DLI0Fra3yS6SkRVQSbHNX9yRGuYG_nKl0Of3arTeJseal89y3IMhFZKaRWgsA0NOIyD_--OjcCwLhKCZ99z7ElB80RO66XfGiCGXL5B18ofnFwr6ZjL3EvQ1sk7lhxGdIoXGUoXB724vuwVEEIrLTXs7RnrWh-Lp9V8GfQ9NOYQEuEZwRQxtisYryyhLZ6ehCz9PRvo-vJD5I_Cy1d7nCSKNRANqamSnxUwJAtKWw4NraxggOvD8LNUQwDks3DzRi9TYLeXBfBDkxaEaVV2jeNf0uNBBJrkQUcRcTLesRpFZvA"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "hgN0SIXexIfH09hHllFynpsrDWckOUAuj9uGc4f4o9HkjxDeXmcSPnJghiDLPpjbojyM0-QiYp9iTQ-XLWXJtAFn8VXlrF3oEKBOCiP8piPP9vppuBKmphGZUIKl0lf4qK3X9YsbiX7G9567Crfu8YOLmgnLgM9UOawPCPwrx2uKtX-KWWJ446r6lf_-fy1gCZGCWFV1fkRxNF4TsV4tPijV9d-ifvd8r7hUdqZWpvDk-2xwl05tVrCiiwMc4mGCSU96pMiZDVj_JH4sKfGxmmMbqHRWVUWYen-Lg2z809Vx4Ig27WJqGX4fmISS1LkPdPXR1kDiIItqw1CzLZJpUQ"}]}}]
# -*- coding:utf-8-*-
from PyQt5.QtGui import QIcon, QPixmap,  QStandardItem, QStandardItemModel
from PyQt5.QtCore import QRect,  QSize, Qt
from PyQt5.Qt import QLineEdit, QAction, QCheckBox
from PyQt5.QtWidgets import QApplication, QWidget, QHeaderView, QPushButton, QMainWindow, \
    QGridLayout, QAbstractItemView, QTableView, QMenuBar, QStatusBar, QTextEdit, QInputDialog
import sys
import time
import requests
import threading
import json
import os
import base64
import shutil
import sqlite3
import re
import random
import cgitb
from urllib.parse import urlencode
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# myTask=[]
def myTasks(myTask,max):
    myResult={}
    count = 0
    myNum=0
    while True:

        for task in myTask:
            if myNum < len(task.keys()):
                temp = 0
                for key in task.keys():
                    if temp == myNum:
                        myResult[key] = task.get(key)
                        break
                    temp += 1
            # else:

        myNum+=1
        if myNum>=max:
            break
        # count += 1
        # if count >=len(myTask):
        #     break

    return myResult
def dpapi_decrypt(encrypted):
    import ctypes
    import ctypes.wintypes
    class DATA_BLOB(ctypes.Structure):
        _fields_ = [('cbData', ctypes.wintypes.DWORD),
                    ('pbData', ctypes.POINTER(ctypes.c_char))]
    p = ctypes.create_string_buffer(encrypted, len(encrypted))
    blobin = DATA_BLOB(ctypes.sizeof(p), p)
    blobout = DATA_BLOB()
    retval = ctypes.windll.crypt32.CryptUnprotectData(
        ctypes.byref(blobin), None, None, None, None, 0, ctypes.byref(blobout))
    if not retval:
        raise ctypes.WinError()
    result = ctypes.string_at(blobout.pbData, blobout.cbData)
    ctypes.windll.kernel32.LocalFree(blobout.pbData)
    return result

def aes_decrypt(filepath, encrypted_txt):
    with open(filepath, encoding='utf-8', mode="r") as f:
        jsn = json.loads(str(f.readline()))
    encoded_key = jsn["os_crypt"]["encrypted_key"]
    encrypted_key = base64.b64decode(encoded_key.encode())
    encrypted_key = encrypted_key[5:]
    key = dpapi_decrypt(encrypted_key)
    nonce = encrypted_txt[3:15]
    cipher = Cipher(algorithms.AES(key), None, backend=default_backend())
    cipher.mode = modes.GCM(nonce)
    decryptor = cipher.decryptor()
    return decryptor.update(encrypted_txt[15:])

def chrome_decrypt(filepath,encrypted_txt):
    if sys.platform == 'win32':
        try:
            if (encrypted_txt[:4] == b'\x01\x00\x00\x00') or (encrypted_txt[:4] == b'x01x00x00x00'):
                decrypted_txt = dpapi_decrypt(encrypted_txt)
                return decrypted_txt.decode()
            elif encrypted_txt[:3] == b'v10':
                decrypted_txt = aes_decrypt(filepath,encrypted_txt)
                return decrypted_txt[:-16].decode()
        except WindowsError:
            return None
    else:
        raise WindowsError
    

def async_call(fn):
    def wrapper(*args, **kwargs):
        t = threading.Thread(target=fn, args=args, kwargs=kwargs)
        t.start()
    return wrapper


if not os.path.exists('userdata'):
    os.makedirs("userdata")


class Response(object):
    def __init__(self):
        self.status_code = -999
        self.text = '网络超时'



class Req(object):
    @staticmethod
    def get(url, headers):
        res = Response()
        count_requests = 3
        while count_requests > 0:
            try:
                res = requests.get(url=url, headers=headers, timeout=10)
                if res.status_code:
                    break
                if 'ゲームトレードへのアクセスが大変混雑しております' in res.text:
                    break
            except:
                time.sleep(1)
            count_requests = count_requests - 1
            print('请求失败，正在重新连接')
            with open('error_requests.txt', 'a+') as f:
                f.write(str(res.status_code) + '-'*10 + res.text)
            time.sleep(3)
        return res

    @staticmethod
    def post(url, headers, data):
        res = Response()
        count_requests = 3
        while count_requests > 0:
            try:
                res = requests.post(url=url, headers=headers, data=data, timeout=10)
                if res.status_code:
                    break
                else:
                    pass

            except:
                time.sleep(1)
            count_requests = count_requests - 1
            print('请求失败，正在重新连接')
            time.sleep(3)
        return res


req = Req()



class Q1(QMainWindow):
    def __init__(self):
        super(Q1, self).__init__()
        create_tb1_cmd = '''
         CREATE TABLE IF NOT EXISTS user (
         id INTEGER PRIMARY KEY,
         username varchar(200),
         status varchar(30),
         remark varchar(255)); '''

        self.conn = sqlite3.connect("userinfo.db", check_same_thread = False)
        self.cursor = self.conn.cursor()       
        self.cursor.execute(create_tb1_cmd)

        create_tb2_cmd = '''
         CREATE TABLE IF NOT EXISTS shop(
         userid varchar(10) PRIMARY KEY,
         sdata varchar(255),
         remark1 varchar(255));
        '''
        self.conn2 = sqlite3.connect("shopinfo.db", check_same_thread = False)
        self.cursor2 = self.conn2.cursor()
        self.cursor2.execute(create_tb2_cmd)
        
        self.width = 700
        self.height = 900
        self.resize(700, 900)
        self.initUi()

##    def closeEvent(self, event):
##        sys.exit(app.exec_())

    def initUi(self):
        self.centralwidget = QWidget()
        self.centralwidget.setObjectName("centralwidget")
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")


        self.setCentralWidget(self.centralwidget)
        self.menubar = QMenuBar()
        self.menubar.setGeometry(QRect(0, 0, 800, 23))
        self.menubar.setObjectName("menubar")
        self.setMenuBar(self.menubar)
        self.statusbar = QStatusBar()
        self.statusbar.setObjectName("statusbar")
        self.setStatusBar(self.statusbar)

        self.tableView = QTableView(self.centralwidget)
        self.tableView.setGeometry(QRect(50, 20, 681, 521))
        self.tableView.setObjectName("tableView")
        self.tableView.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)  # 行高自动
        self.tableView.setSelectionMode(QAbstractItemView.SingleSelection)  # 设置只能选中一行
        self.tableView.setSelectionBehavior(QAbstractItemView.SelectRows)  # 设置只有行选中
        self.tableView.setEditTriggers(QTableView.NoEditTriggers)  # 不可编辑
        self.tableView.horizontalHeader().setVisible(True)
        self.tableView.clicked.connect(self.on_sel_line)
        self.tableView.doubleClicked.connect((lambda: self.to_add_shop()))
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("此处会记录日志")

        self.add_button = QPushButton(self)
        self.add_button.setText('新增账号')

        self.add_button.clicked.connect(self.add_user)
        self.del_button = QPushButton(self)
        self.del_button.setText('删除账号')
        self.del_button.clicked.connect(self.del_row_data)
    
        self.login_button = QPushButton(self)
        self.login_button.setText('登录')
        self.login_button.clicked.connect(self.web_login)
        self.login_button.setStyleSheet('''QWidget{background-color:#3BB9FF;color: white;}''')

        self.pause_button = QPushButton(self)
        self.pause_button.setText('暂停')
        self.pause_button.clicked.connect((lambda: self.to_pause()) )

        self.cookie_button = QPushButton(self)
        self.cookie_button.setText('获取缓存')
        self.cookie_button.clicked.connect((lambda: self.get_sec_cookie()) )
        
        self.start_button = QPushButton(self)
        self.start_button.setText('执行')
        self.start_button.clicked.connect((lambda: self.start()) )
        self.start_button.setStyleSheet('''QWidget{background-color:#85BB65;color: white;}''')
        self.del_button.setStyleSheet('''QWidget{background-color:#FF0000;color: white;}''')

        self.allsec_button = QPushButton(self)
        self.allsec_button.clicked.connect(self.allsec)
        self.allsec_button.setText('全部选中')

        self.setting_button = QPushButton(self)
        self.setting_button.clicked.connect(self.to_setting)
        self.setting_button.setText('参数设置')
        
        self.gridLayout.addWidget(self.tableView,       0, 0, 10, 8)
        ##self.gridLayout.addWidget(self.text_edit,      11, 0, 5,  8)
        
        self.gridLayout.addWidget(self.start_button,   16, 0, 1, 1)
        self.gridLayout.addWidget(self.pause_button,   16, 1, 1, 1)
        self.gridLayout.addWidget(self.login_button,   16, 2, 1, 1)
        self.gridLayout.addWidget(self.cookie_button, 16, 3, 1, 1)
        self.gridLayout.addWidget(self.setting_button,  16, 4, 1, 1)
        self.gridLayout.addWidget(self.allsec_button,  16, 5, 1, 1)
        self.gridLayout.addWidget(self.add_button,     16, 6, 1, 1)
        self.gridLayout.addWidget(self.del_button,     16, 7, 1, 1)


        
        self.sub = SubWindow()
        self.sub2 = SubWindow2()

        for s in [self.add_button, self.del_button, self.login_button, self.start_button,
                  self.allsec_button, self.setting_button, self.pause_button, self.cookie_button]:
            s.setMinimumSize(QSize(0, 48))

        self.titles1 = []
        self.model = QStandardItemModel()
        self.model.setHorizontalHeaderLabels(['序号', '名称', '选择'])
        self.model.itemChanged.connect(self.OnCheckBoxItemChanged)

        self.tableView.horizontalHeader().setStyleSheet(
            "QHeaderView::section{background-color:rgb(155, 194, 230);color: black;};")

        self.setWindowTitle("gametrade")
        try:
            with open('check.json', 'r') as f:
                self.checklist = json.loads(f.read())
        except:
            self.checklist = []
        self.tasklist = []
        self.task_shangjia = {}
        self.task_xiajia = {}
        self.mul_shangjia_thread =[]
        self.mul_dian_thread = []
        self.mul_like_thread = []
        self.sec_row = []
        self.task_dianzan_per = []
        self.task_dianzhan = []
        self.task_check_like = []
        self.ischeck_shangjia = False
        self.ischeck_dianzhan = False
        self.ischeck_xiajia = False
        self.is_lock = False
        self.is_running = False
        self.d_num = 1
        self.count_err = 0
        self.count_err_shangjia = 0
        self.per_dianzan_list = []
        self.adsl_lock = False
        self.log_lines = 0
        self.get_shop_url()

        try:
            with open('cookie_dict.json', 'r') as f:
                self.cookie_dict = json.loads(f.read())
        except:
            self.cookie_dict = {}

        try:
            with open('userid_dict.json', 'r') as f:
                self.userid_dict = json.loads(f.read())
        except:
            self.userid_dict = {}
            
            
        try:
            self.sign = requests.get(url="https://116.236.32.219:99/api/token/15", verify=False).text
        except:
            self.sign = ''

        try:
            self.sleeptime = int(self.sub2.value1)
        except:
            self.sleeptime = 2

        try:
            self.per_time = int(self.sub2.value2) * 60
        except:
            self.per_time = 1800

        try:
            with open('run_task.json', 'r') as f:
                self.task = json.loads(f.read())
                print(self.task,"-----------------------")
            self.task_shangjia = self.task["shangjia"]
            self.task_xiajia = self.task["xiajia"]
            self.task_dianzhan = self.task["dianzhan"]

            for t in self.task_xijia:
                self.task_xiajia[t][0] = 0

            for t in self.task_shangjia:
                if  self.task_shangjia[t][4] == 1:
                    self.task_shangjia[t][4] = 0
                    self.task_shangjia[t][3] = int(time.time())
                    
        except Exception as e:
            self.task_shangjia = {}

        if self.task_shangjia != {}:
            self.start()


    def getcookiefromchrome(self, host, index):   
        sql = 'SELECT name, encrypted_value as value FROM cookies where host_key like "%{}%"'.format(host)
        filename = r'userdata\{}\default\Cookies'.format(str(index))
        if not os.path.exists(filename):
            filename = r'userdata\{}\default\Network\Cookies'.format(str(index))
        print(index, host, filename)
        while True:
            try:
                con = sqlite3.connect(filename)
                con.row_factory = sqlite3.Row
                cur = con.cursor()
                cur.execute(sql)
                cookie = ''
                filepath = r'userdata\{0}\Local State'.format(str(index))
                for row in cur:
                    if row['value'] is not None:
                        name = row['name']
                        value = chrome_decrypt(filepath,row['value'])
                        if value is not None:
                            cookie += name + '=' + value + ';'
                return cookie
            except Exception as e:
                print(e)
                cookie = ''
            if str(index) in self.task_cookie:
                self.task_cookie.remove(str(index))
            return cookie


            
    def get_sec_cookie(self):
        if self.checklist == []:
            self.write_log_to_Text('请先选择需要读取缓存的账号！')
            return
        
        for c in self.checklist:
            cookie = self.getcookiefromchrome('gametrade.jp', str(c))
            if 'session_id' in cookie:
                self.write_log_to_Text(str(c) + '  缓存获取成功！')
            else:
                self.write_log_to_Text(str(c) + '  缓存获取出错，请重打开浏览器！')
            self.cookie_dict[str(c)] = cookie
        with open('cookie_dict.json', 'w') as f:
            f.write(json.dumps(self.cookie_dict))        



    def get_random_time(self):
        if self.sleeptime <= 0:
            return random.uniform(0, 0)
            
        try:
            self.min_time = int(self.sleeptime) - 1
            self.min_time = 0 if self.min_time < 1 else self.min_time
            self.max_time = int(self.sleeptime) + 1
        except:
            self.max_time, self.min_time = 10, 5
        return random.uniform(self.min_time, self.max_time)

    def resizeEvent(self, evt):
        self.width = evt.size().width()
        self.height = evt.size().height() - 100
        self.model_init()
        self.update_data()

    def model_init(self):
        self.row = 0
        self.model.removeRows(0, self.model.rowCount())
        self.model.setColumnCount(3)
        self.tableView.setModel(self.model)
        self.tableView.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    
    def to_add_shop(self):
        self.on_sel_line()
        self.sub.init_data(self.sec_row)
        self.sub.show()

    def to_setting(self):
        self.sub2.show()

    def get_shop_url(self):
        try:
            sdata = self.cursor2.execute("select userid, sdata from shop").fetchall()
            self.sdict = {s[0]: json.loads(s[1]) for s in sdata}
        except Exception as e:
            self.write_log_to_Text(e)

    def update_data(self):
        self.model_init()
        try:
            self.rlist = self.cursor.execute("select id,username,status from user order by id desc").fetchall()
            self.row = 0
            for t in self.rlist:
                for column in range(3):
                    if column == 2:
                        item_checked = QStandardItem()
                        if str(t[0]) not in self.checklist:
                            item_checked.setCheckState(Qt.Unchecked)
                        else:
                            item_checked.setCheckState(Qt.Checked)
                        item_checked.setCheckable(True)
                        self.model.setItem(self.row, column, item_checked)

                    else:
                        i = QStandardItem(str(t[column]))
                        self.model.setItem(self.row, column, i)
                self.row = self.row + 1
            self.tableView.setModel(self.model)
        except Exception as e:
            self.write_log_to_Text(e)

    def on_sel_line(self):
        self.sec_row = self.rlist[self.tableView.currentIndex().row()]

    def del_row_data(self):
        if not self.sec_row:
            self.write_log_to_Text('请先点击要删除的行！')
            return
        try:
            ##os.popen("taskkill /f /im chrome* >nul 2>nul")
            for c in self.checklist:
                shutil.rmtree(os.getcwd() + '\\userdata\\' + str(c))
        except Exception as e:
            self.write_log_to_Text(e)

        try:
            for c in self.checklist:
                self.cursor.execute("delete from user  where id = {0} ".format(str(c)))
            self.conn.commit()
            self.checklist = []
            with open('check.json', 'w') as f:
                f.write(json.dumps(self.checklist))
        except Exception as e:
            self.write_log_to_Text(e)
        self.sec_row = []
        self.update_data()
        return

    def allsec(self):
        if self.allsec_button.text() == '全部选中':
            self.checklist = [str(r[0]) for r in self.rlist]
            self.allsec_button.setText('全部取消')
        else:
            self.allsec_button.setText('全部选中')
            self.checklist = []
        self.update_data()

    def OnCheckBoxItemChanged(self, item):
        try:
            rowNum = item.row()
            colNum = item.column()
            if colNum == 2:
                if self.model.item(rowNum, 2).checkState() == 2:
                    self.checklist.append(self.model.item(rowNum, 0).text())
                if self.checklist and self.model.item(rowNum, 2).checkState() == 0:
                    try:
                        self.checklist.pop(self.checklist.index(self.model.item(rowNum, 0).text()))
                    except:
                        pass
                self.checklist = list(set(self.checklist))
                with open('check.json', 'w') as f:
                    f.write(json.dumps(self.checklist))
        except Exception as e:
            self.write_log_to_Text(e)

    def add_user(self, event):
        items = []
        value, ok = QInputDialog.getItem(self, "输入框", "请输入账号名称:", items, 1, True)
        if value.strip() == '':
            return
        try:
            strsql = "insert into user(username) values('{0}' )".format(value)
            self.cursor.execute(strsql)
            self.conn.commit()
            self.update_data()
        except Exception as e:
            self.write_log_to_Text(e)

    def web_login(self):
        if self.checklist == []:
            self.write_log_to_Text('请先选择需要登录的账号！')
            return

        try:
            for c in self.checklist:
                p = [r[1] for r in self.rlist if str(r[0]) == str(c)][0]
                default_url = '"https://gametrade.jp/signin"'
                os.popen(os.getcwd() + '\\chrome\\chrome.exe ' + default_url + '  --user-data-dir="' + os.getcwd() + '\\userdata\\' + c + '"')
        except Exception as e:
            self.write_log_to_Text(e)


    @async_call
    def adsl(self):
        if not self.adsl_lock:
            if self.sub2.value6 == '':
                return
            self.adsl_lock = True
            self.is_running = False
            self.write_log_to_Text('重新拨号中......')
            os.system('@Rasdial 宽带连接 /DISCONNECT') 
            os.system('@Rasdial 宽带连接 {0} {1}'.format(self.sub2.value6, self.sub2.value7 ))
            time.sleep(9)
            self.adsl_lock = False
            self.is_running = True
            self.count_err = 0
            self.count_err_shangjia = 0
        else:
            return

    def to_pause(self):
        if self.is_running == True:
            self.pause_button.setText('继续')
            self.is_running = False
            return
        if self.is_running == False:
            self.pause_button.setText('暂停')
            self.is_running = True
            return

        
    @async_call
    def reboot_sys(self):
        self.is_running = False
        self.write_log_to_Text('等待任务池结束，准备重启......')
        stime = 1
        while True:
            try:
                if (self.mul_shangjia_thread + self.mul_dian_thread + self.mul_like_thread) == []:
                    task = {"shangjia": self.task_shangjia, "xiajia": self.task_xiajia, "dianzhan":self.task_dianzhan}
                    with open('run_task.json', 'w') as f:
                        f.write(json.dumps(task))
                    break
                time.sleep(3)
                stime = stime + 1
                if stime > 9:
                    task = {"shangjia": self.task_shangjia, "xiajia": self.task_xiajia, "dianzhan":self.task_dianzhan}
                    with open('run_task.json', 'w') as f:
                        f.write(json.dumps(task))
                    break
            except Exception as e:
                self.write_log_to_Text(str(e))
                return
                
        self.write_log_to_Text('5秒后系统会重启动......')
        os.system('shutdown -r -t 5')

      

    def start(self):
        self.text_edit.setText('')
        if self.checklist == []:
            self.write_log_to_Text('请先选择账号！')
            return
        
        if 'gametrade' in self.sign:
            time.sleep(4)
            self.close()
            return
        
        if not os.path.exists('run_task.json'):
            self.task_shangjia = {}
            self.task_xiajia = {}
            self.task_dianzhan = []
            
        self.mul_shangjia_thread = []
        self.mul_dian_thread = []
        self.sub2 = SubWindow2()
        self.tasklist = []
        self.mul_like_thread = []
        self.sec_row = []
        self.task_dianzan_per = []
        self.task_check_like = []
        self.ischeck_shangjia = False
        self.ischeck_dianzhan = False
        self.ischeck_xiajia = False
        self.is_lock = False
        self.is_running = False
        self.d_num = int(self.sub2.value3)
        self.per_dianzan_list = []
        self.shangjia()
        self.xiajia()
        self.dianzhan()


    @async_call
    def test(self):
        self.write_log_to_Text('开始重新读取所有账')

    @async_call    
    def shangjia(self): 
        if self.cookie_dict == {}:
            self.write_log_to_Text('开始重新读取所有账号缓存.......')
            self.task_cookie = [str(r[0]) for r in self.rlist]
           
            while True:
                if self.task_cookie == []:
                    with open('cookie_dict.json', 'w') as f:
                        f.write(json.dumps(self.cookie_dict))
                    break
                k = self.task_cookie[0]
                print('缓存:' + k)
                cookie = self.getcookiefromchrome('gametrade.jp', k)
                self.cookie_dict[k] = cookie
                if k in self.task_cookie:
                    self.task_cookie.remove(k)

        self.write_log_to_Text('开始上架！')
        if len([k for k in self.cookie_dict if self.cookie_dict[k] not in ['null', '']]) < self.d_num:
               self.write_log_to_Text('点赞的数据不可以大于登录的账号！')
               return
        self.stime = time.time()


        if not os.path.exists('run_task.json'):
            self.checklist = sorted([int(c) for c in set(self.checklist)])
            self.checklist = [str(c) for c in self.checklist]
            self.myTask=[]
            maxValue=0
            for c in self.checklist:
                urls = list(self.sdict.get(str(c), []))
                urls = [url.strip() for url in urls]
                if len(urls)>maxValue:
                    maxValue=len(urls)
                temp={}
                for url in urls:
                    uu = [u.strip() for u in url.split(' ') if u.strip() != '']
                    self.task_shangjia["_".join([c,uu[0]])] = [c, uu[0], '', int(time.time()) - 100000, 0] ##[账号id, 商品链接， 发布后链接，上一次发布时间， 是否处理中]
                    temp["_".join([c,uu[0]])] = [c, uu[0], '', int(time.time()) - 100000, 0]

                    try:
                        pertime  = int(uu[1]) * 60
                    except Exception as e:
                        pertime  = int(self.sub2.value2) * 60
                self.task_xiajia[c] = [0, int(time.time()) - 100000] ##是否在执行1,0,  上一次下架完的时间点
                self.myTask.append(temp)
            self.task_shangjia=myTasks(self.myTask,maxValue)
            print("---------------")
        else:
            os.remove('run_task.json')
            self.adsl()


        if not self.task_shangjia:
            self.write_log_to_Text('勾选的账号下面没有商品')
            return
          
        self.is_running = True
        self.pause_button.setText('暂停')

        self.xiajia_status = False
 

        while True:
            for k in self.task_shangjia:
                slist = self.task_shangjia[k]
                # print(self.is_running,"   1")
                # print(slist[3] + int(self.sub2.value2) * 60 < time.time(),"   2")
                # print(slist[4] == 0,"     3")
                # print(len(self.mul_shangjia_thread) < int(self.sub2.value4), "         4")
                # print(len(self.task_dianzhan) <= int(self.sub2.value4) * int(self.sub2.value3) * 2,"      5")
                if self.is_running and slist[3] + int(self.sub2.value2) * 60 < time.time() \
                        and slist[4] == 0 and len(self.mul_shangjia_thread) < int(self.sub2.value4) \
                        and len(self.task_dianzhan) <=int(self.sub2.value4) * int(self.sub2.value3) * 2:

                    self.th_shangjia(slist[0:2])
                    self.mul_shangjia_thread.append(slist[0:2])
                    slist[4] = 1

            time.sleep(self.get_random_time())

            for k in self.task_shangjia:
                slist = self.task_shangjia[k]
                if slist[4] == 1:
                    self.xiajia_status = False
                    break
                if (slist[3] + int(self.sub2.value2) * 60 )< time.time():
                    self.xiajia_status = False
                    break
                self.xiajia_status = True
                                
            print(int(time.time()) - slist[3], len(self.mul_shangjia_thread), len(self.mul_dian_thread),len(self.task_dianzhan), len(self.mul_like_thread), self.xiajia_status)

    @async_call
    def xiajia(self):
        while True:
            for k in self.task_xiajia:
                tlist = self.task_xiajia[k]
                if self.is_running and (tlist[1] + int(self.sub2.value9) * 60 <= int(time.time())) and ( len(self.mul_like_thread) < int(self.sub2.value8)) and (tlist[0] == 0) and self.xiajia_status == True and len(self.task_dianzhan) == 0:
                    self.th_xiajia(k)
                    self.mul_like_thread.append(k)
                    self.task_xiajia[k][0] = 1 
            time.sleep(self.get_random_time())

    @async_call
    def dianzhan(self):
        while True:
            for ulist in self.task_dianzhan:
                if (len(self.mul_dian_thread) < self.d_num) and ulist not in self.mul_dian_thread:
                    self.th_dianzhan(ulist)
            time.sleep(self.get_random_time())
                    
                
        

    @async_call
    def th_shangjia(self, tlist):
        if not self.is_running:
            if tlist in self.mul_shangjia_thread:
                self.mul_shangjia_thread.remove(tlist)
            self.task_shangjia["_".join(tlist)][4] = 0
            return
            
        c = tlist[0]
        url = tlist[1]
        try:
            cookie = self.cookie_dict.get(str(c), '')
            headers = {
                "Host": "gametrade.jp",
                "Connection": "keep-alive",
                "Cache-Control": "max-age=0",
                "sec-ch-ua": "\"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\"",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "sec-ch-ua-mobile": "?0",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.64",
                "sec-ch-ua-platform": "\"Windows\"",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Dest": "empty",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Cookie": "_session_id=98a58fa05201680921605a231247b0f9; _gcl_au=1.1.250756049.1683151368; _gid=GA1.2.525652246.1683151371; __gads=ID=fde00c581bbf5eef-22ac9c2995e00084:T=1683151371:RT=1683151371:S=ALNI_MaPTDz1H7v6fDPFL8gu25ie_pqEbA; __gpi=UID=00000c0152fe9d54:T=1683151371:RT=1683151371:S=ALNI_MYYUpoMPuAzLLt3O8L-VXw15u7cXQ; remember_token=845ff154cf818cc375cf527feccf6d7ec7ee81ec; recently_viewed_exhibits=53826586%2C53826629%2C53826635; _ga_LM6MGFW0PR=GS1.1.1683151370.1.1.1683153666.60.0.0; _ga=GA1.2.825830596.1683151371; _gat_UA-85898721-1=1",
                "If-None-Match": "W/\"e623d14378fc5a290efaa4664a8b83fa\""
            }
            headers2 = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
                "accept-language": "zh-CN,zh;q=0.9",
                "cache-control": "max-age=0",
                "Content-Type": "application/x-www-form-urlencoded",
                "cookie": "",
                "Origin": "https://gametrade.jp",
                "Referer": "https://gametrade.jp/messages/5270281",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.108 Safari/537.36"
            }
            headers["Cookie"] = cookie
            headers2["cookie"] = cookie
            self.write_log_to_Text('开始对产品{}上架'.format(str(c) + '_' + url))
            res = req.get(url, headers)

            if res.status_code == 429 or 'ゲームトレードへのアクセスが大変混雑しております' in res.text:
                self.write_log_to_Text('下架失败，开始重新拨号')
                self.adsl()
                if tlist in self.mul_shangjia_thread:
                    self.mul_shangjia_thread.remove(tlist)
                self.task_shangjia["_".join(tlist)][4] = 0
                return
                            
            if res.history != []:
                self.write_log_to_Text('任务{}需要重新登录'.format(str(c)))
                if tlist in self.mul_shangjia_thread:
                    self.mul_shangjia_thread.remove(tlist)
                self.task_shangjia["_".join(tlist)][4] = 0
                return
            post_url = re.findall(r'<form class="relist_form" action="(/exhibits.*?)"', res.text, re.S)[0]
            # post_url = re.findall(r'<form action="(/exhibitsits.*?)"', res.text, re.S)[0]
            # authenticity_token = re.findall(r'<form action="/exhibits.*?<input type="hidden" name="authenticity_token" value="(.*?)"', res.text, re.S)[0]
            authenticity_token = re.findall(r'<form class="relist_form" action="/exhibits.*?<input type="hidden" name="authenticity_token" value="(.*?)"', res.text, re.S)[0]



            ##读取useid并记录
            if self.userid_dict.get(str(c)) == None:
                userid =  re.findall(r'\'user_id\': \"(\d+)\"',res.text,re.S)
                if userid:
                    self.userid_dict[str(c)] = str(userid[0])
                    with open('userid_dict.json', 'w') as f:
                        f.write(json.dumps(self.userid_dict))
                else:
                    self.write_log_to_Text('任务{}需要重新登录'.format(str(c)))
                    if tlist in self.mul_shangjia_thread:
                        self.mul_shangjia_thread.remove(tlist)
                    self.task_shangjia["_".join(tlist)][4] = 0


            headers2['Referer'] = url
            data = urlencode({'utf8': '✓', 'authenticity_token':authenticity_token})
            res = req.post("https://gametrade.jp" + post_url, headers2, data)
            self.res2 = res
            print(res)
            time.sleep(100)
            success_url = res.history[0].headers.get('Location')
            if success_url == url:
                self.write_log_to_Text('上架出错：' + url)
     
                
            elif success_url == "https://gametrade.jp/":
                self.write_log_to_Text('上架出错,账号{}cookie无效'.format(str(c)))
                
            else:
                self.write_log_to_Text('上架成功：' + str(c) + '_' + url + '_'+ success_url)
                self.task_shangjia["_".join(tlist)][2] = success_url
                self.task_shangjia["_".join(tlist)][3] = int(time.time())
        
                    
                for dlist in [[d, success_url] for d in random.sample(self.cookie_dict.keys(), self.d_num)]:
                    self.task_dianzhan.append(dlist + [c])


        except Exception as e:
            self.write_log_to_Text(str(e) + "上架出错:" + "_".join(tlist))
            if tlist in self.mul_shangjia_thread:
                self.mul_shangjia_thread.remove(tlist)
            time.sleep(2)
            if self.is_running:
                ##检测网络是否通
                s_time = time.time()
                cmd= "ping -n 3 -w 1 www.qq.com"
                network_status = False
                while True:
                    exit_code = os.system(cmd)
                    if exit_code:
                        e_time = time.time()
                        if e_time - s_time > 15:
                            break
                        time.sleep(5)
                    else:
                        network_status = True
                        break
                if network_status == False:
                    self.write_log_to_Text('网络不通')
                    self.reboot_sys()


            self.count_err_shangjia = self.count_err_shangjia  + 1
            if (self.count_err_shangjia ) > 6 and (self.adsl_lock == False):
                self.write_log_to_Text('上架失败开始重新拨号')
                self.adsl()
            with open('err_request.txt', 'w', encoding='utf8') as f:
                f.write(str(res.status_code) + '\n' + res.text)
                        
        if tlist in self.mul_shangjia_thread:
            self.mul_shangjia_thread.remove(tlist)
        self.task_shangjia["_".join(tlist)][4] = 0



    @async_call 
    def th_dianzhan(self, ulist):
        self.mul_dian_thread.append(ulist)
        if not self.is_running:
            if ulist  in self.mul_dian_thread:
                self.mul_dian_thread.remove(ulist)
            return
        

        try:
            c = ulist[0]
            url = ulist[1]
            mul_c =ulist[2]
            cookie = self.cookie_dict.get(c, '')
            headers ={
                    "Host": "gametrade.jp",
                    "Connection": "keep-alive",
                    "Cache-Control": "max-age=0",
                    "sec-ch-ua": "\"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\"",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "sec-ch-ua-mobile": "?0",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.64",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Dest": "empty",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "Cookie": "_session_id=98a58fa05201680921605a231247b0f9; _gcl_au=1.1.250756049.1683151368; _gid=GA1.2.525652246.1683151371; __gads=ID=fde00c581bbf5eef-22ac9c2995e00084:T=1683151371:RT=1683151371:S=ALNI_MaPTDz1H7v6fDPFL8gu25ie_pqEbA; __gpi=UID=00000c0152fe9d54:T=1683151371:RT=1683151371:S=ALNI_MYYUpoMPuAzLLt3O8L-VXw15u7cXQ; remember_token=845ff154cf818cc375cf527feccf6d7ec7ee81ec; recently_viewed_exhibits=53826586%2C53826629%2C53826635; _ga_LM6MGFW0PR=GS1.1.1683151370.1.1.1683153666.60.0.0; _ga=GA1.2.825830596.1683151371; _gat_UA-85898721-1=1",
                    "If-None-Match": "W/\"e623d14378fc5a290efaa4664a8b83fa\""
                }
            headers["Cookie"] = cookie
            res = req.get(url, headers)

            if res.status_code == 429 or 'ゲームトレードへのアクセスが大変混雑しております' in res.text:
                self.write_log_to_Text('下架失败，开始重新拨号')
                if ulist in self.mul_dian_thread:
                    self.mul_dian_thread.remove(ulist)
                self.adsl()
                return
                
            try:
                cf_token = re.findall(r'csrf-token\" content=\"(.*?)\"', res.text, re.S)[0]
                post_url = re.findall(r'<form action=\"(/exhibits.*?/thinkings)"', res.text, re.S)[0]
            except:
                cf_token, post_url = '', ''

            headers2 = {
                    'accept': '*/*;q=0.5, text/javascript, application/javascript, application/ecmascript, application/x-ecmascript',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'cache-control': 'max-age=0',
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'cookie': cookie,
                    'Origin': 'https://gametrade.jp',
                    'Referer': url,
                    'Upgrade-Insecure-Requests': '1',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.108 Safari/537.36',
                    'x-csrf-token': cf_token,
                    'x-requested-with': 'XMLHttpRequest'
                    }
            data = urlencode({'utf8':'✓', 'button':''})
            res = req.post("https://gametrade.jp" + post_url, headers2, data)
            res.encoding = 'utf8'

            if res.status_code == 200:
                if ulist in self.mul_dian_thread:
                    self.mul_dian_thread.remove(ulist)
                if ulist in self.task_dianzhan:
                    self.task_dianzhan.remove(ulist)
                self.write_log_to_Text(url + ' 点赞成功')
                return
            else:
                self.write_log_to_Text(url + ' ' + str(res.status_code) +  ' 点赞失败,重新执行')
                if ulist in self.mul_dian_thread:
                    print(ulist,self.mul_dian_thread,'**************remove!')
                    self.mul_dian_thread.remove(ulist)
                    print(self.mul_dian_thread, '**************')
                with open('err_like.txt', 'w') as f:
                    f.write(str(res.status_code) + '\n' + res.text)
                self.adsl()     
        except Exception as e:
            if ulist in self.mul_dian_thread:
                self.mul_dian_thread.remove(ulist)
            if self.is_running:
                ##检测网络是否通
                s_time = time.time()
                cmd= "ping -n 3 -w 1 www.qq.com"
                network_status = False
                while True:
                    exit_code = os.system(cmd)
                    if exit_code:
                        e_time = time.time()
                        if e_time - s_time > 15:
                            break
                        time.sleep(5)
                    else:
                        network_status = True
                        break
                if network_status == False:
                    self.write_log_to_Text('网络不通')
                    self.reboot_sys()
            self.write_log_to_Text(e)                         
        if ulist in self.mul_dian_thread:
            self.mul_dian_thread.remove(ulist)
        return
        


    @async_call 
    def th_xiajia(self, c):
        if not self.is_running:
            if ulist in self.mul_like_thread:
                self.mul_like_thread.remove(ulist)
            return
        cookie = self.cookie_dict.get(c, '')

        headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'max-age=0',
        'cookie':'',
        'if-none-match': 'W/"7780797c1a681f8660ee6bf7daf6924e"',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.108 Safari/537.36'
        }

        headers2  ={
                    "Host": "gametrade.jp",
                    "Connection": "keep-alive",
                    "Cache-Control": "max-age=0",
                    "sec-ch-ua": "\"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\"",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "sec-ch-ua-mobile": "?0",
                    "Upgrade-Insecure-Requests": "1",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.64",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Dest": "empty",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "Cookie": "_session_id=98a58fa05201680921605a231247b0f9; _gcl_au=1.1.250756049.1683151368; _gid=GA1.2.525652246.1683151371; __gads=ID=fde00c581bbf5eef-22ac9c2995e00084:T=1683151371:RT=1683151371:S=ALNI_MaPTDz1H7v6fDPFL8gu25ie_pqEbA; __gpi=UID=00000c0152fe9d54:T=1683151371:RT=1683151371:S=ALNI_MYYUpoMPuAzLLt3O8L-VXw15u7cXQ; remember_token=845ff154cf818cc375cf527feccf6d7ec7ee81ec; recently_viewed_exhibits=53826586%2C53826629%2C53826635; _ga_LM6MGFW0PR=GS1.1.1683151370.1.1.1683153666.60.0.0; _ga=GA1.2.825830596.1683151371; _gat_UA-85898721-1=1",
                    "If-None-Match": "W/\"e623d14378fc5a290efaa4664a8b83fa\""
                }
            
        headers["cookie"] = cookie
        headers2["Cookie"] = cookie
        page = 3
        num = 0
        all_num = len(self.sdict.get(str(c)))
        try:
            while True:
                url = "https://gametrade.jp/users/{0}?page={1}".format(self.userid_dict.get(str(c)), str(page))
                res = req.get(url, headers)
                aa = res.text
                bb = re.findall(r'<li class=\"box-shadow\">(.*?)</div></a></li>',aa,re.S)
                ##items = re.findall(r'<li class=\"box-shadow\"><a href=\"(.*?)\".*?<div class=\"game-image\">.*?<i class=\".*?fa-heart\"></i><span>(.*?)</span></button>',aa,re.S)
                cc = 0
                for b in bb:
                    item = [re.findall(r'<a href=\"(.*?)\"><div class=\"game-image\">',b)[0], "".join(re.findall(r'<i class=\".*?fa-heart\"></i><span>(.*?)</span></button>',b))]
                    if item[1].strip() == '' and 'いいね' not in b:
                        cc = cc + 1
                        continue
                        
                    try:
                        likes = int(item[1].replace('×', ''))
                    except:
                        likes = 0


                    if likes <= int(self.sub2.value3):
                        self.write_log_to_Text("_".join([c, item[0]]) + ' 点赞没有上涨开始下架')
                        edit_url ="https://gametrade.jp/exhibits/{}/edit".format(item[0].split('/')[-1])
                        res = req.get(edit_url, headers2)
                        if res.status_code == 429 or 'ゲームトレードへのアクセスが大変混雑しております' in res.text:
                            self.write_log_to_Text('下架失败，开始重新拨号')
                            self.adsl()
                            
                        try:
                            post_url = re.findall(r'<form action=\"(/exhibits[^>]*?/cancel_exhibit)"', res.text, re.S)[0]
                            authenticity_token = re.findall(r'<form action="/exhibits[^>]*?/cancel_exhibit.*?<input type="hidden" name="authenticity_token" value="(.*?)"', res.text, re.S)[0]
                        except:
                            authenticity_token, post_url = '', ''
                            continue

                        headers3 = {
                                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3',
                                'accept-encoding': 'gzip, deflate, br',
                                'accept-language': 'zh-CN,zh;q=0.9',
                                'cache-control': 'max-age=0',
                                'content-length': '242',
                                'content-type': 'application/x-www-form-urlencoded',
                                'cookie': cookie,
                                'origin': 'https://gametrade.jp',
                                'Referer': edit_url,
                                'upgrade-insecure-requests': '1',
                                'user-agent': 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.108 Safari/537.36'}
                        

                        
                        data = urlencode({'utf8': '✓', '_method': 'patch', 'authenticity_token':authenticity_token, 'commit': '出品をキャンセルする' })
                        ##print("https://gametrade.jp" + post_url, data)
                        res = req.post("https://gametrade.jp" + post_url, headers3, data)
                        num = num + 1
                        self.write_log_to_Text("_".join([c, item[0]]) + ' 下架成功！' + str(num) + ' - ' + str(all_num) )
                        time.sleep(self.get_random_time())
                        
                    else:
                        cc = cc + 1

                if num > all_num:
                    ##每次下架的个数和上架的uRL长度相同
                    break
                if len(bb) == 0:                           
                    break
                if cc == len(bb):
                    print('翻页.....')
                    page = page + 1

        except Exception as e:
            self.write_log_to_Text(c + ' ' + str(e))
            if self.is_running:
                ##检测网络是否通
                s_time = time.time()
                cmd= "ping -n 3 -w 1 www.qq.com"
                network_status = False
                while True:
                    exit_code = os.system(cmd)
                    if exit_code:
                        e_time = time.time()
                        if e_time - s_time > 15:
                            break
                        time.sleep(5)
                    else:
                        network_status = True
                        break
                if network_status == False:
                    self.write_log_to_Text('网络不通')
                    self.reboot_sys()
                    
        if c in self.mul_like_thread:
            self.mul_like_thread.remove(c)
        self.task_xiajia[c][0] = 0
        self.task_xiajia[c][1] = int(time.time())
        print('任务结束')  
        return
        


    def get_current_time(self):
        current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        return current_time

    # 日志动态打印
    @async_call 
    def write_log_to_Text(self, logmsg):
        current_time = self.get_current_time()
        logmsg_in = str(current_time) + " " + str(logmsg) + "\n"
        print(logmsg_in)
        ##self.text_edit.insertPlainText(logmsg_in)
        self.log_lines =  self.log_lines + 1
        with open('run.log', 'a+') as f:
            f.write(logmsg_in + '\n')


class SubWindow(QMainWindow):
    def __init__(self):
        super(SubWindow, self).__init__()
        self.centralwidget = QWidget()
        self.centralwidget.setObjectName("centralwidget")
        self.width = 600
        self.height = 1000
        self.setWindowOpacity(0.95)

        self.setWindowTitle("上架商品")
        icon = QIcon()
        # icon.addPixmap(QPixmap("images/data.png"), QIcon.Normal, QIcon.Off)
        self.setWindowIcon(icon)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        self.initUi()

    def initUi(self):
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")
        self.setCentralWidget(self.centralwidget)
        self.resize(self.width, self.height)
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("一行一个链接")
        self.gridLayout.addWidget(self.text_edit, 0, 0, 8, 1)

        self.add_button = QPushButton(self)
        self.add_button.setText('确认')
        self.gridLayout.addWidget(self.add_button, 9, 0, 1, 1)
        self.add_button.clicked.connect(self.add_shopitem)

    def init_data(self, rlist):
        self.text_edit.clear()
        self.setWindowTitle("商品链接_" + rlist[1])
        self.userid = rlist[0]
        self.text_edit.insertPlainText("\n".join(ui.sdict.get(str(self.userid), '')))

    def add_shopitem(self):
        sdata = json.dumps([i.strip() for i in self.text_edit.toPlainText().split('\n') if i.strip() != ''])
        try:
            strsql = "insert into shop(userid, sdata) values('{0}', '{1}') ON CONFLICT(userid)DO UPDATE SET sdata='{1}' ".format(self.userid, sdata)
            ui.cursor2.execute(strsql)
            ui.conn2.commit()
            ui.sdict[str(self.userid)] = json.loads(sdata)
        except Exception as e:
            ui.write_log_to_Text("添加商品失败：" + str(e))
        ui.get_shop_url()
        self.hide()

class SubWindow2(QMainWindow):
    def __init__(self):
        super(SubWindow2, self).__init__()
        self.initUi()

    def initUi(self):
        self.width = 300
        self.height = 400
        self.setWindowOpacity(0.95)      
        self.setWindowTitle("参数设定")
        self.centralwidget = QWidget()
        self.centralwidget.setObjectName("centralwidget")
        self.setCentralWidget(self.centralwidget)
        self.resize(self.width, self.height)
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")
      
        self.input1 = QLineEdit(self)
        self.input1.setAlignment(Qt.AlignCenter)
        self.input1.setPlaceholderText('单次请求间隔时间(不填默认为1秒)')

        self.input2 = QLineEdit(self)
        self.input2.setAlignment(Qt.AlignCenter)
        self.input2.setPlaceholderText('N分钟无点赞下架(不填默认为30分钟)')

        self.input3 = QLineEdit(self)
        self.input3.setAlignment(Qt.AlignCenter)
        self.input3.setPlaceholderText('点赞数量(默认5个)')

        self.input4 = QLineEdit(self)
        self.input4.setAlignment(Qt.AlignCenter)
        self.input4.setPlaceholderText('上架线程数(默认1个)')

        self.input5 = QLineEdit(self)
        self.input5.setAlignment(Qt.AlignCenter)
        self.input5.setPlaceholderText('点赞线程数(默认5个)')

        self.input6 = QLineEdit(self)
        self.input6.setAlignment(Qt.AlignCenter)
        self.input6.setPlaceholderText('宽带账号')

        self.input7 = QLineEdit(self)
        self.input7.setAlignment(Qt.AlignCenter)
        self.input7.setPlaceholderText('宽带密码')

        self.input8 = QLineEdit(self)
        self.input8.setAlignment(Qt.AlignCenter)
        self.input8.setPlaceholderText('下架线程数')

        self.input9 = QLineEdit(self)
        self.input9.setAlignment(Qt.AlignCenter)
        self.input9.setPlaceholderText('下架间隔(分钟)')

        
        self.add_button = QPushButton(self)
        self.add_button.setText('确 认')
        self.add_button.clicked.connect(self.save_setting)

        
        self.gridLayout.addWidget(self.input1,     0, 0, 1, 1)
        self.gridLayout.addWidget(self.input2,     1, 0, 1, 1)
        self.gridLayout.addWidget(self.input3,     2, 0, 1, 1)
        self.gridLayout.addWidget(self.input4,     3, 0, 1, 1)
        self.gridLayout.addWidget(self.input5,     4, 0, 1, 1)
        self.gridLayout.addWidget(self.input6,     5, 0, 1, 1)
        self.gridLayout.addWidget(self.input7,     6, 0, 1, 1)
        self.gridLayout.addWidget(self.input8,     7, 0, 1, 1)
        self.gridLayout.addWidget(self.input9,     8, 0, 1, 1)
        self.gridLayout.addWidget(self.add_button, 9, 0, 1, 1)
        self.add_button.setFocus()

        try:
            with open('setting.json', 'r') as f:
                sdict = json.loads(f.read())
            self.value1 = sdict.get('v1')
            self.value2 = sdict.get('v2')
            self.value3 = sdict.get('v3')
            self.value4 = sdict.get('v4')
            self.value5 = sdict.get('v5')
            self.value6 = sdict.get('v6')
            self.value7 = sdict.get('v7')
            self.value8 = sdict.get('v8')
            self.value9 = sdict.get('v9')
            self.input1.setText(self.value1)
            self.input2.setText(self.value2)
            self.input3.setText(self.value3)
            self.input4.setText(self.value4)
            self.input5.setText(self.value5)
            self.input6.setText(self.value6)
            self.input7.setText(self.value7)
            self.input8.setText(self.value8)
            self.input9.setText(self.value9)
        except:
            self.value1 = 1
            self.value2 = 20
            self.value3 = 5
            self.value4 = 8
            self.value5 = 8
            self.value6 = ''
            self.value7 = ''
            self.value8 = 3
            self.value9 = 10

    def save_setting(self):
        value1 = self.input1.text().strip()
        value2 = self.input2.text().strip()
        value3 = self.input3.text().strip()
        value4 = self.input4.text().strip()
        value5 = self.input5.text().strip()
        value6 = self.input6.text().strip()
        value7 = self.input7.text().strip()
        value8 = self.input8.text().strip()
        value9 = self.input9.text().strip()
        try:
            value1 = str(int(value1))
        except:
            value1 = '1'

        try:
            value2 = str(int(value2))
        except:
            value2 = '30'

        try:
            value3 = str(int(value3))
        except:
            value3 = '5'

        try:
            value4 = str(int(value4))
        except:
            value4 = '1'

        try:
            value5 = str(int(value5))
        except:
            value5 = '5'

        try:
            value8 = str(int(value8))
        except:
            value8 = '3'

        try:
            value9 = str(int(value9))
        except:
            value9 = '10'
            
        with open('setting.json', 'w') as f:
            f.write(json.dumps({"v1": value1,  "v2": value2, "v3":  value3, "v4":  value4, "v5":  value5, "v6":  value6, "v7":  value7, "v8":  value8, "v9":  value9}))
        self.hide()
    

if __name__ == '__main__':
    if not os.path.exists('log'):
        os.mkdir('log')
    cgitb.enable(format='text', logdir= 'log')
    app = QApplication(sys.argv)
    icon = QIcon()
    icon.addPixmap(QPixmap("favicon.ico"), QIcon.Normal, QIcon.Off)
    ui = Q1()
    ui.setWindowIcon(icon)
    ui.show()
    sys.exit(app.exec_())


